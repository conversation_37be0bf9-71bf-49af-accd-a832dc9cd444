name: T5DE

on: push

jobs:
  build:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
        with:
            fetch-depth: 0

      - name: Get Version
        id: version
        uses: ietf-tools/semver-action@v1
        if: github.ref_type != 'tag'
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          branch: master
          noVersionBumpBehavior: current
          noNewCommitBehavior: current

      - name: Get Semver Version
        id: semver_version
        uses: dhkatz/get-version-action@v3.0.0

      - name: Setup T5DE Version
        if: github.ref_type != 'tag'
        run: |
          echo "T5DE_VERSION=${{ steps.version.outputs.nextStrict }}${{ format('+{0}-{1}', github.ref_name, github.run_id) }}" >> $env:GITHUB_ENV

      - name: Setup T5DE Version
        if: github.ref_type == 'tag'
        run: |
          echo "T5DE_VERSION=${{ steps.semver_version.outputs.version-without-v }}" >> $env:GITHUB_ENV

      - name: Setup IMVU Version
        run: |
          $ENV:IMVU_VERSION = $(cat VERSION)
          echo ("IMVU_VERSION=" + $ENV:IMVU_VERSION) >> $ENV:GITHUB_ENV

      - name: Setup App Version
        run: echo "APP_VERSION=${{ env.T5DE_VERSION }}-${{ env.IMVU_VERSION }}" >> $env:GITHUB_ENV

      - name: Echo Versions
        run: |
          echo "T5DE_VERSION=${{ env.T5DE_VERSION }}"
          echo "IMVU_VERSION=${{ env.IMVU_VERSION }}"
          echo "APP_VERSION=${{ env.APP_VERSION }}"

      - name: Setup Python 2.7
        uses: LizardByte/setup-python-action@master
        with:
          python-version: '2.7'
      - run: pip install -r requirements.txt

      - name: Patch Installer
        run: |
          python -m t5de --patch

      - name: Install NSIS
        run: |
          iwr -useb get.scoop.sh -outfile 'install.ps1'
          .\install.ps1 -RunAsAdmin
          scoop update
          scoop bucket add extras
          scoop install nsis

      - name: Build Installer
        uses: joncloud/makensis-action@v4.1
        with:
          script-file: "./scripts/install.nsi"

      - name: Upload Artifact
        uses: actions/upload-artifact@v4.6.1
        with:
          name: T5DE-${{ env.APP_VERSION }}.exe
          path: T5DE-${{ env.APP_VERSION }}.exe

      - name: Upload Release
        uses: softprops/action-gh-release@v2
        if: github.ref_type == 'tag'
        with:
          files: T5DE-${{ env.APP_VERSION }}.exe
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
