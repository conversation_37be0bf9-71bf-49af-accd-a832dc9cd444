from ...patch import InterfacePatch


class SocialNetworkMapperPatch(InterfacePatch):
    """
    Maps social connections between users to help identify relationship networks
    and find users through their social connections.
    """
    def __init__(self):
        super(SocialNetworkMapperPatch, self).__init__()
        
        # Add social network mapping functionality
        self.register('SOCIAL_MAPPER_JS', 'main/client.js', 'imvu.socialInit')
        self.register('FRIEND_NETWORK_HTML', 'dialogs/social_network/index.html', 'social-network-container')
        self.register('FRIEND_TRACKING', 'dialogs/friends/friends.js', 'onFriendAdded')

    def patch(self, context):
        if context.pattern == 'SOCIAL_MAPPER_JS':
            context.write(context.line)
            context.write('\n// Social Network Mapping System\n')
            context.write('window.socialNetworkMapper = {\n', indent=0)
            context.write('socialGraph: {},\n', indent=1)
            context.write('userConnections: {},\n', indent=1)
            context.write('mutualFriends: {},\n', indent=1)
            context.write('socialClusters: {},\n', indent=1)
            context.write('influenceScores: {},\n', indent=1)
            context.write('\n', indent=1)
            context.write('init: function() {\n', indent=1)
            context.write('this.loadFromStorage();\n', indent=2)
            context.write('this.startMapping();\n', indent=2)
            context.write('this.setupPeriodicAnalysis();\n', indent=2)
            context.write('console.log("Social Network Mapper initialized");\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Add connection between users\n', indent=1)
            context.write('addConnection: function(userId1, userId2, connectionType, strength) {\n', indent=1)
            context.write('connectionType = connectionType || "friend";\n', indent=2)
            context.write('strength = strength || 1;\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Initialize user nodes if they don\'t exist\n', indent=2)
            context.write('if (!this.socialGraph[userId1]) {\n', indent=2)
            context.write('this.socialGraph[userId1] = {\n', indent=3)
            context.write('connections: {},\n', indent=4)
            context.write('connectionCount: 0,\n', indent=4)
            context.write('influenceScore: 0,\n', indent=4)
            context.write('clusters: []\n', indent=4)
            context.write('};\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (!this.socialGraph[userId2]) {\n', indent=2)
            context.write('this.socialGraph[userId2] = {\n', indent=3)
            context.write('connections: {},\n', indent=4)
            context.write('connectionCount: 0,\n', indent=4)
            context.write('influenceScore: 0,\n', indent=4)
            context.write('clusters: []\n', indent=4)
            context.write('};\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Add bidirectional connection\n', indent=2)
            context.write('this.socialGraph[userId1].connections[userId2] = {\n', indent=2)
            context.write('type: connectionType,\n', indent=3)
            context.write('strength: strength,\n', indent=3)
            context.write('established: new Date().toISOString(),\n', indent=3)
            context.write('interactions: 0\n', indent=3)
            context.write('};\n', indent=2)
            context.write('\n', indent=2)
            context.write('this.socialGraph[userId2].connections[userId1] = {\n', indent=2)
            context.write('type: connectionType,\n', indent=3)
            context.write('strength: strength,\n', indent=3)
            context.write('established: new Date().toISOString(),\n', indent=3)
            context.write('interactions: 0\n', indent=3)
            context.write('};\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Update connection counts\n', indent=2)
            context.write('this.socialGraph[userId1].connectionCount++;\n', indent=2)
            context.write('this.socialGraph[userId2].connectionCount++;\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Update mutual friends cache\n', indent=2)
            context.write('this.updateMutualFriends(userId1, userId2);\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Recalculate influence scores\n', indent=2)
            context.write('this.calculateInfluenceScore(userId1);\n', indent=2)
            context.write('this.calculateInfluenceScore(userId2);\n', indent=2)
            context.write('\n', indent=2)
            context.write('this.saveToStorage();\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Track interaction between users\n', indent=1)
            context.write('trackInteraction: function(userId1, userId2, interactionType) {\n', indent=1)
            context.write('if (this.socialGraph[userId1] && this.socialGraph[userId1].connections[userId2]) {\n', indent=2)
            context.write('this.socialGraph[userId1].connections[userId2].interactions++;\n', indent=3)
            context.write('this.socialGraph[userId1].connections[userId2].lastInteraction = new Date().toISOString();\n', indent=3)
            context.write('this.socialGraph[userId1].connections[userId2].lastInteractionType = interactionType;\n', indent=3)
            context.write('\n', indent=3)
            context.write('// Strengthen connection based on interaction\n', indent=3)
            context.write('var strengthIncrease = this.getStrengthIncrease(interactionType);\n', indent=3)
            context.write('this.socialGraph[userId1].connections[userId2].strength += strengthIncrease;\n', indent=3)
            context.write('this.socialGraph[userId2].connections[userId1].strength += strengthIncrease;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Get strength increase based on interaction type\n', indent=1)
            context.write('getStrengthIncrease: function(interactionType) {\n', indent=1)
            context.write('var strengthMap = {\n', indent=2)
            context.write('"message": 0.1,\n', indent=3)
            context.write('"room_together": 0.05,\n', indent=3)
            context.write('"gift": 0.3,\n', indent=3)
            context.write('"profile_view": 0.02,\n', indent=3)
            context.write('"photo_like": 0.05,\n', indent=3)
            context.write('"invite": 0.15\n', indent=3)
            context.write('};\n', indent=2)
            context.write('return strengthMap[interactionType] || 0.01;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Update mutual friends cache\n', indent=1)
            context.write('updateMutualFriends: function(userId1, userId2) {\n', indent=1)
            context.write('var user1Connections = Object.keys(this.socialGraph[userId1].connections);\n', indent=2)
            context.write('var user2Connections = Object.keys(this.socialGraph[userId2].connections);\n', indent=2)
            context.write('\n', indent=2)
            context.write('var mutuals = user1Connections.filter(function(friendId) {\n', indent=2)
            context.write('return user2Connections.indexOf(friendId) !== -1;\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('var key = [userId1, userId2].sort().join("-");\n', indent=2)
            context.write('this.mutualFriends[key] = mutuals;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Get mutual friends between two users\n', indent=1)
            context.write('getMutualFriends: function(userId1, userId2) {\n', indent=1)
            context.write('var key = [userId1, userId2].sort().join("-");\n', indent=2)
            context.write('return this.mutualFriends[key] || [];\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Calculate influence score for a user\n', indent=1)
            context.write('calculateInfluenceScore: function(userId) {\n', indent=1)
            context.write('var user = this.socialGraph[userId];\n', indent=2)
            context.write('if (!user) return 0;\n', indent=2)
            context.write('\n', indent=2)
            context.write('var score = 0;\n', indent=2)
            context.write('var connections = user.connections;\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Base score from connection count\n', indent=2)
            context.write('score += user.connectionCount * 10;\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Bonus for connection strength\n', indent=2)
            context.write('Object.keys(connections).forEach(function(friendId) {\n', indent=2)
            context.write('var connection = connections[friendId];\n', indent=3)
            context.write('score += connection.strength * 5;\n', indent=3)
            context.write('score += connection.interactions * 2;\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Bonus for being connected to influential users\n', indent=2)
            context.write('Object.keys(connections).forEach(function(friendId) {\n', indent=2)
            context.write('var friendInfluence = window.socialNetworkMapper.influenceScores[friendId] || 0;\n', indent=3)
            context.write('score += friendInfluence * 0.1;\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('user.influenceScore = score;\n', indent=2)
            context.write('this.influenceScores[userId] = score;\n', indent=2)
            context.write('return score;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Find users through social connections\n', indent=1)
            context.write('findUserThroughConnections: function(targetUserId, maxDegrees) {\n', indent=1)
            context.write('maxDegrees = maxDegrees || 3;\n', indent=2)
            context.write('var paths = [];\n', indent=2)
            context.write('var visited = new Set();\n', indent=2)
            context.write('\n', indent=2)
            context.write('function findPaths(currentUserId, targetId, currentPath, degree) {\n', indent=2)
            context.write('if (degree > maxDegrees) return;\n', indent=3)
            context.write('if (visited.has(currentUserId)) return;\n', indent=3)
            context.write('if (currentUserId === targetId) {\n', indent=3)
            context.write('paths.push(currentPath.slice());\n', indent=4)
            context.write('return;\n', indent=4)
            context.write('}\n', indent=3)
            context.write('\n', indent=3)
            context.write('visited.add(currentUserId);\n', indent=3)
            context.write('currentPath.push(currentUserId);\n', indent=3)
            context.write('\n', indent=3)
            context.write('var user = window.socialNetworkMapper.socialGraph[currentUserId];\n', indent=3)
            context.write('if (user && user.connections) {\n', indent=3)
            context.write('Object.keys(user.connections).forEach(function(friendId) {\n', indent=4)
            context.write('findPaths(friendId, targetId, currentPath, degree + 1);\n', indent=5)
            context.write('});\n', indent=4)
            context.write('}\n', indent=3)
            context.write('\n', indent=3)
            context.write('currentPath.pop();\n', indent=3)
            context.write('visited.delete(currentUserId);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Start search from current user\n', indent=2)
            context.write('if (window.imvu && window.imvu.currentUser) {\n', indent=2)
            context.write('findPaths(window.imvu.currentUser.id, targetUserId, [], 0);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('return paths;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Get user\'s social circle\n', indent=1)
            context.write('getSocialCircle: function(userId, depth) {\n', indent=1)
            context.write('depth = depth || 2;\n', indent=2)
            context.write('var circle = new Set();\n', indent=2)
            context.write('var toProcess = [{ userId: userId, currentDepth: 0 }];\n', indent=2)
            context.write('\n', indent=2)
            context.write('while (toProcess.length > 0) {\n', indent=2)
            context.write('var current = toProcess.shift();\n', indent=3)
            context.write('if (current.currentDepth >= depth) continue;\n', indent=3)
            context.write('\n', indent=3)
            context.write('var user = this.socialGraph[current.userId];\n', indent=3)
            context.write('if (!user) continue;\n', indent=3)
            context.write('\n', indent=3)
            context.write('Object.keys(user.connections).forEach(function(friendId) {\n', indent=3)
            context.write('if (!circle.has(friendId)) {\n', indent=4)
            context.write('circle.add(friendId);\n', indent=5)
            context.write('toProcess.push({ userId: friendId, currentDepth: current.currentDepth + 1 });\n', indent=5)
            context.write('}\n', indent=4)
            context.write('});\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('return Array.from(circle);\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Detect social clusters\n', indent=1)
            context.write('detectClusters: function() {\n', indent=1)
            context.write('var clusters = [];\n', indent=2)
            context.write('var processed = new Set();\n', indent=2)
            context.write('\n', indent=2)
            context.write('Object.keys(this.socialGraph).forEach(function(userId) {\n', indent=2)
            context.write('if (processed.has(userId)) return;\n', indent=3)
            context.write('\n', indent=3)
            context.write('var cluster = window.socialNetworkMapper.getConnectedComponent(userId, processed);\n', indent=3)
            context.write('if (cluster.length > 2) {\n', indent=3)
            context.write('clusters.push({\n', indent=4)
            context.write('id: "cluster_" + clusters.length,\n', indent=5)
            context.write('members: cluster,\n', indent=5)
            context.write('size: cluster.length,\n', indent=5)
            context.write('density: window.socialNetworkMapper.calculateClusterDensity(cluster)\n', indent=5)
            context.write('});\n', indent=4)
            context.write('}\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('this.socialClusters = clusters;\n', indent=2)
            context.write('return clusters;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Get connected component starting from a user\n', indent=1)
            context.write('getConnectedComponent: function(startUserId, processed) {\n', indent=1)
            context.write('var component = [];\n', indent=2)
            context.write('var toVisit = [startUserId];\n', indent=2)
            context.write('\n', indent=2)
            context.write('while (toVisit.length > 0) {\n', indent=2)
            context.write('var userId = toVisit.shift();\n', indent=3)
            context.write('if (processed.has(userId)) continue;\n', indent=3)
            context.write('\n', indent=3)
            context.write('processed.add(userId);\n', indent=3)
            context.write('component.push(userId);\n', indent=3)
            context.write('\n', indent=3)
            context.write('var user = this.socialGraph[userId];\n', indent=3)
            context.write('if (user && user.connections) {\n', indent=3)
            context.write('Object.keys(user.connections).forEach(function(friendId) {\n', indent=4)
            context.write('if (!processed.has(friendId)) {\n', indent=5)
            context.write('toVisit.push(friendId);\n', indent=6)
            context.write('}\n', indent=5)
            context.write('});\n', indent=4)
            context.write('}\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('return component;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Calculate cluster density\n', indent=1)
            context.write('calculateClusterDensity: function(cluster) {\n', indent=1)
            context.write('var totalPossibleConnections = cluster.length * (cluster.length - 1) / 2;\n', indent=2)
            context.write('var actualConnections = 0;\n', indent=2)
            context.write('\n', indent=2)
            context.write('for (var i = 0; i < cluster.length; i++) {\n', indent=2)
            context.write('for (var j = i + 1; j < cluster.length; j++) {\n', indent=3)
            context.write('var user1 = this.socialGraph[cluster[i]];\n', indent=4)
            context.write('if (user1 && user1.connections[cluster[j]]) {\n', indent=4)
            context.write('actualConnections++;\n', indent=5)
            context.write('}\n', indent=4)
            context.write('}\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('return totalPossibleConnections > 0 ? actualConnections / totalPossibleConnections : 0;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Start mapping social connections\n', indent=1)
            context.write('startMapping: function() {\n', indent=1)
            context.write('// Hook into friend events\n', indent=2)
            context.write('if (window.imvu && window.imvu.events) {\n', indent=2)
            context.write('window.imvu.events.on("friend.added", this.onFriendAdded.bind(this));\n', indent=3)
            context.write('window.imvu.events.on("friend.removed", this.onFriendRemoved.bind(this));\n', indent=3)
            context.write('window.imvu.events.on("message.sent", this.onMessageSent.bind(this));\n', indent=3)
            context.write('window.imvu.events.on("room.together", this.onRoomTogether.bind(this));\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Event handlers\n', indent=1)
            context.write('onFriendAdded: function(userId, friendId) {\n', indent=1)
            context.write('this.addConnection(userId, friendId, "friend", 1.0);\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('onFriendRemoved: function(userId, friendId) {\n', indent=1)
            context.write('if (this.socialGraph[userId] && this.socialGraph[userId].connections[friendId]) {\n', indent=2)
            context.write('delete this.socialGraph[userId].connections[friendId];\n', indent=3)
            context.write('this.socialGraph[userId].connectionCount--;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('if (this.socialGraph[friendId] && this.socialGraph[friendId].connections[userId]) {\n', indent=2)
            context.write('delete this.socialGraph[friendId].connections[userId];\n', indent=3)
            context.write('this.socialGraph[friendId].connectionCount--;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('onMessageSent: function(senderId, recipientId) {\n', indent=1)
            context.write('this.trackInteraction(senderId, recipientId, "message");\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('onRoomTogether: function(userId1, userId2) {\n', indent=1)
            context.write('this.trackInteraction(userId1, userId2, "room_together");\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Storage functions\n', indent=1)
            context.write('loadFromStorage: function() {\n', indent=1)
            context.write('try {\n', indent=2)
            context.write('var stored = localStorage.getItem("socialNetworkMapper");\n', indent=3)
            context.write('if (stored) {\n', indent=3)
            context.write('var data = JSON.parse(stored);\n', indent=4)
            context.write('this.socialGraph = data.socialGraph || {};\n', indent=4)
            context.write('this.userConnections = data.userConnections || {};\n', indent=4)
            context.write('this.mutualFriends = data.mutualFriends || {};\n', indent=4)
            context.write('this.socialClusters = data.socialClusters || {};\n', indent=4)
            context.write('this.influenceScores = data.influenceScores || {};\n', indent=4)
            context.write('}\n', indent=3)
            context.write('} catch (e) {\n', indent=2)
            context.write('console.error("Failed to load social network data:", e);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('saveToStorage: function() {\n', indent=1)
            context.write('try {\n', indent=2)
            context.write('var data = {\n', indent=3)
            context.write('socialGraph: this.socialGraph,\n', indent=4)
            context.write('userConnections: this.userConnections,\n', indent=4)
            context.write('mutualFriends: this.mutualFriends,\n', indent=4)
            context.write('socialClusters: this.socialClusters,\n', indent=4)
            context.write('influenceScores: this.influenceScores,\n', indent=4)
            context.write('lastSaved: new Date().toISOString()\n', indent=4)
            context.write('};\n', indent=3)
            context.write('localStorage.setItem("socialNetworkMapper", JSON.stringify(data));\n', indent=3)
            context.write('} catch (e) {\n', indent=2)
            context.write('console.error("Failed to save social network data:", e);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('setupPeriodicAnalysis: function() {\n', indent=1)
            context.write('setInterval(function() {\n', indent=2)
            context.write('window.socialNetworkMapper.detectClusters();\n', indent=3)
            context.write('window.socialNetworkMapper.saveToStorage();\n', indent=3)
            context.write('}, 300000); // Analyze every 5 minutes\n', indent=2)
            context.write('}\n', indent=1)
            context.write('};\n', indent=0)
            context.write('\n')
            context.write('// Initialize social network mapper\n')
            context.write('window.socialNetworkMapper.init();\n')
            
        elif context.pattern == 'FRIEND_NETWORK_HTML':
            context.write('<div class="social-network-container">', indent=0)
            context.write('<div class="network-header">', indent=1)
            context.write('<h2>Social Network Map</h2>', indent=2)
            context.write('<input type="text" id="network-user-search" placeholder="Search user to map network...">', indent=2)
            context.write('</div>', indent=1)
            context.write('<div class="network-visualization" id="network-viz"></div>', indent=1)
            context.write('<div class="network-analysis">', indent=1)
            context.write('<div class="analysis-panel">', indent=2)
            context.write('<h3>Network Analysis</h3>', indent=3)
            context.write('<div id="network-stats"></div>', indent=3)
            context.write('</div>', indent=2)
            context.write('<div class="mutual-friends-panel">', indent=2)
            context.write('<h3>Mutual Friends</h3>', indent=3)
            context.write('<div id="mutual-friends-list"></div>', indent=3)
            context.write('</div>', indent=2)
            context.write('<div class="influence-panel">', indent=2)
            context.write('<h3>Influence Scores</h3>', indent=3)
            context.write('<div id="influence-rankings"></div>', indent=3)
            context.write('</div>', indent=2)
            context.write('</div>', indent=1)
            context.write('</div>', indent=0)
            
        elif context.pattern == 'FRIEND_TRACKING':
            context.write(context.line)
            context.write('\n// Track friend additions for social mapping\n')
            context.write('if (window.socialNetworkMapper) {\n', indent=0)
            context.write('window.socialNetworkMapper.addConnection(currentUserId, friendId, "friend", 1.0);\n', indent=1)
            context.write('}\n', indent=0)
