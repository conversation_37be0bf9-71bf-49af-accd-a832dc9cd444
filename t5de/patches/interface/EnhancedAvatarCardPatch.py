from ...patch import InterfacePatch


class EnhancedAvatarCardPatch(InterfacePatch):
    """
    Enhances the avatar card with additional user information including
    last seen, status history, activity patterns, and social connections.
    """
    def __init__(self):
        super(EnhancedAvatarCardPatch, self).__init__()
        
        # Enhance avatar card HTML structure
        self.register('ENHANCED_AVATAR_HTML', 'dialogs/avatar_card/index.html', 'avatar-info-panel')
        self.register('ENHANCED_AVATAR_JS', 'dialogs/avatar_card/avatarCard.js', 'loadAvatarInfo')
        self.register('ENHANCED_AVATAR_CSS', 'dialogs/avatar_card/avatarCard.css', 'avatar-info-panel')

    def patch(self, context):
        if context.pattern == 'ENHANCED_AVATAR_HTML':
            context.write(context.line)
            context.write('<div class="enhanced-info-panel">', indent=6)
            context.write('<div class="info-tabs">', indent=7)
            context.write('<button class="tab-btn active" data-tab="basic">Basic</button>', indent=8)
            context.write('<button class="tab-btn" data-tab="activity">Activity</button>', indent=8)
            context.write('<button class="tab-btn" data-tab="social">Social</button>', indent=8)
            context.write('<button class="tab-btn" data-tab="history">History</button>', indent=8)
            context.write('</div>', indent=7)
            context.write('<div class="tab-content">', indent=7)
            context.write('<div class="tab-panel active" id="basic-tab">', indent=8)
            context.write('<div class="info-row">', indent=9)
            context.write('<label>Last Seen:</label>', indent=10)
            context.write('<span id="last-seen-time"></span>', indent=10)
            context.write('</div>', indent=9)
            context.write('<div class="info-row">', indent=9)
            context.write('<label>Time Zone:</label>', indent=10)
            context.write('<span id="user-timezone"></span>', indent=10)
            context.write('</div>', indent=9)
            context.write('<div class="info-row">', indent=9)
            context.write('<label>Typical Online Hours:</label>', indent=10)
            context.write('<span id="typical-hours"></span>', indent=10)
            context.write('</div>', indent=9)
            context.write('<div class="info-row">', indent=9)
            context.write('<label>Account Age:</label>', indent=10)
            context.write('<span id="account-age"></span>', indent=10)
            context.write('</div>', indent=9)
            context.write('<div class="info-row">', indent=9)
            context.write('<label>Profile Views:</label>', indent=10)
            context.write('<span id="profile-views"></span>', indent=10)
            context.write('</div>', indent=9)
            context.write('</div>', indent=8)
            context.write('<div class="tab-panel" id="activity-tab">', indent=8)
            context.write('<div class="activity-chart" id="activity-chart"></div>', indent=9)
            context.write('<div class="activity-stats">', indent=9)
            context.write('<div class="stat-item">', indent=10)
            context.write('<label>Average Session:</label>', indent=11)
            context.write('<span id="avg-session"></span>', indent=11)
            context.write('</div>', indent=10)
            context.write('<div class="stat-item">', indent=10)
            context.write('<label>Most Active Day:</label>', indent=11)
            context.write('<span id="most-active-day"></span>', indent=11)
            context.write('</div>', indent=10)
            context.write('<div class="stat-item">', indent=10)
            context.write('<label>Favorite Rooms:</label>', indent=11)
            context.write('<div id="favorite-rooms"></div>', indent=11)
            context.write('</div>', indent=10)
            context.write('</div>', indent=9)
            context.write('</div>', indent=8)
            context.write('<div class="tab-panel" id="social-tab">', indent=8)
            context.write('<div class="social-stats">', indent=9)
            context.write('<div class="stat-item">', indent=10)
            context.write('<label>Friends Count:</label>', indent=11)
            context.write('<span id="friends-count"></span>', indent=11)
            context.write('</div>', indent=10)
            context.write('<div class="stat-item">', indent=10)
            context.write('<label>Mutual Friends:</label>', indent=11)
            context.write('<span id="mutual-friends"></span>', indent=11)
            context.write('</div>', indent=10)
            context.write('<div class="stat-item">', indent=10)
            context.write('<label>Common Rooms:</label>', indent=11)
            context.write('<div id="common-rooms"></div>', indent=11)
            context.write('</div>', indent=10)
            context.write('<div class="stat-item">', indent=10)
            context.write('<label>Social Score:</label>', indent=11)
            context.write('<span id="social-score"></span>', indent=11)
            context.write('</div>', indent=10)
            context.write('</div>', indent=9)
            context.write('<div class="friend-network" id="friend-network"></div>', indent=9)
            context.write('</div>', indent=8)
            context.write('<div class="tab-panel" id="history-tab">', indent=8)
            context.write('<div class="history-timeline" id="history-timeline"></div>', indent=9)
            context.write('<div class="interaction-history">', indent=9)
            context.write('<div class="history-section">', indent=10)
            context.write('<h4>Recent Interactions</h4>', indent=11)
            context.write('<div id="recent-interactions"></div>', indent=11)
            context.write('</div>', indent=10)
            context.write('<div class="history-section">', indent=10)
            context.write('<h4>Status Changes</h4>', indent=11)
            context.write('<div id="status-history"></div>', indent=11)
            context.write('</div>', indent=10)
            context.write('</div>', indent=9)
            context.write('</div>', indent=8)
            context.write('</div>', indent=7)
            context.write('</div>', indent=6)
            
        elif context.pattern == 'ENHANCED_AVATAR_JS':
            context.write(context.line)
            context.write('\n// Enhanced avatar card functionality\n')
            context.write('var enhancedAvatarCard = {\n', indent=0)
            context.write('currentUserId: null,\n', indent=1)
            context.write('userCache: {},\n', indent=1)
            context.write('\n', indent=1)
            context.write('init: function(userId) {\n', indent=1)
            context.write('this.currentUserId = userId;\n', indent=2)
            context.write('this.setupTabs();\n', indent=2)
            context.write('this.loadUserData(userId);\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('setupTabs: function() {\n', indent=1)
            context.write('var tabBtns = document.querySelectorAll(".tab-btn");\n', indent=2)
            context.write('var tabPanels = document.querySelectorAll(".tab-panel");\n', indent=2)
            context.write('\n', indent=2)
            context.write('tabBtns.forEach(function(btn) {\n', indent=2)
            context.write('btn.addEventListener("click", function() {\n', indent=3)
            context.write('var targetTab = this.getAttribute("data-tab");\n', indent=4)
            context.write('\n', indent=4)
            context.write('// Remove active class from all tabs and panels\n', indent=4)
            context.write('tabBtns.forEach(function(b) { b.classList.remove("active"); });\n', indent=4)
            context.write('tabPanels.forEach(function(p) { p.classList.remove("active"); });\n', indent=4)
            context.write('\n', indent=4)
            context.write('// Add active class to clicked tab and corresponding panel\n', indent=4)
            context.write('this.classList.add("active");\n', indent=4)
            context.write('document.getElementById(targetTab + "-tab").classList.add("active");\n', indent=4)
            context.write('});\n', indent=3)
            context.write('});\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('loadUserData: function(userId) {\n', indent=1)
            context.write('var user = imvu.userCache[userId];\n', indent=2)
            context.write('if (!user) return;\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Load basic info\n', indent=2)
            context.write('this.loadBasicInfo(user);\n', indent=2)
            context.write('// Load activity data\n', indent=2)
            context.write('this.loadActivityData(user);\n', indent=2)
            context.write('// Load social data\n', indent=2)
            context.write('this.loadSocialData(user);\n', indent=2)
            context.write('// Load history data\n', indent=2)
            context.write('this.loadHistoryData(user);\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('loadBasicInfo: function(user) {\n', indent=1)
            context.write('// Last seen time\n', indent=2)
            context.write('var lastSeenEl = document.getElementById("last-seen-time");\n', indent=2)
            context.write('if (lastSeenEl && user.lastSeen) {\n', indent=2)
            context.write('var lastSeen = new Date(user.lastSeen);\n', indent=3)
            context.write('var now = new Date();\n', indent=3)
            context.write('var timeDiff = now - lastSeen;\n', indent=3)
            context.write('lastSeenEl.textContent = this.formatTimeDifference(timeDiff);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Time zone estimation\n', indent=2)
            context.write('var timezoneEl = document.getElementById("user-timezone");\n', indent=2)
            context.write('if (timezoneEl) {\n', indent=2)
            context.write('var estimatedTz = this.estimateTimezone(user);\n', indent=3)
            context.write('timezoneEl.textContent = estimatedTz || "Unknown";\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Typical online hours\n', indent=2)
            context.write('var typicalHoursEl = document.getElementById("typical-hours");\n', indent=2)
            context.write('if (typicalHoursEl) {\n', indent=2)
            context.write('var hours = this.getTypicalHours(user);\n', indent=3)
            context.write('typicalHoursEl.textContent = hours || "Unknown";\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Account age\n', indent=2)
            context.write('var accountAgeEl = document.getElementById("account-age");\n', indent=2)
            context.write('if (accountAgeEl && user.createdDate) {\n', indent=2)
            context.write('var created = new Date(user.createdDate);\n', indent=3)
            context.write('var age = new Date() - created;\n', indent=3)
            context.write('accountAgeEl.textContent = this.formatTimeDifference(age);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('loadActivityData: function(user) {\n', indent=1)
            context.write('// Create activity chart\n', indent=2)
            context.write('this.createActivityChart(user);\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Average session length\n', indent=2)
            context.write('var avgSessionEl = document.getElementById("avg-session");\n', indent=2)
            context.write('if (avgSessionEl) {\n', indent=2)
            context.write('var avgSession = this.calculateAverageSession(user);\n', indent=3)
            context.write('avgSessionEl.textContent = avgSession || "Unknown";\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Most active day\n', indent=2)
            context.write('var mostActiveDayEl = document.getElementById("most-active-day");\n', indent=2)
            context.write('if (mostActiveDayEl) {\n', indent=2)
            context.write('var mostActiveDay = this.getMostActiveDay(user);\n', indent=3)
            context.write('mostActiveDayEl.textContent = mostActiveDay || "Unknown";\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Favorite rooms\n', indent=2)
            context.write('var favoriteRoomsEl = document.getElementById("favorite-rooms");\n', indent=2)
            context.write('if (favoriteRoomsEl) {\n', indent=2)
            context.write('var favoriteRooms = this.getFavoriteRooms(user);\n', indent=3)
            context.write('favoriteRoomsEl.innerHTML = this.renderRoomList(favoriteRooms);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('loadSocialData: function(user) {\n', indent=1)
            context.write('// Friends count\n', indent=2)
            context.write('var friendsCountEl = document.getElementById("friends-count");\n', indent=2)
            context.write('if (friendsCountEl && user.friendsCount) {\n', indent=2)
            context.write('friendsCountEl.textContent = user.friendsCount.toString();\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Mutual friends\n', indent=2)
            context.write('var mutualFriendsEl = document.getElementById("mutual-friends");\n', indent=2)
            context.write('if (mutualFriendsEl) {\n', indent=2)
            context.write('var mutualFriends = this.getMutualFriends(user);\n', indent=3)
            context.write('mutualFriendsEl.textContent = mutualFriends.length.toString();\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Common rooms\n', indent=2)
            context.write('var commonRoomsEl = document.getElementById("common-rooms");\n', indent=2)
            context.write('if (commonRoomsEl) {\n', indent=2)
            context.write('var commonRooms = this.getCommonRooms(user);\n', indent=3)
            context.write('commonRoomsEl.innerHTML = this.renderRoomList(commonRooms);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Social score\n', indent=2)
            context.write('var socialScoreEl = document.getElementById("social-score");\n', indent=2)
            context.write('if (socialScoreEl) {\n', indent=2)
            context.write('var socialScore = this.calculateSocialScore(user);\n', indent=3)
            context.write('socialScoreEl.textContent = socialScore + "/100";\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('loadHistoryData: function(user) {\n', indent=1)
            context.write('// Create timeline\n', indent=2)
            context.write('this.createHistoryTimeline(user);\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Recent interactions\n', indent=2)
            context.write('var interactionsEl = document.getElementById("recent-interactions");\n', indent=2)
            context.write('if (interactionsEl) {\n', indent=2)
            context.write('var interactions = this.getRecentInteractions(user);\n', indent=3)
            context.write('interactionsEl.innerHTML = this.renderInteractions(interactions);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Status history\n', indent=2)
            context.write('var statusHistoryEl = document.getElementById("status-history");\n', indent=2)
            context.write('if (statusHistoryEl) {\n', indent=2)
            context.write('var statusHistory = this.getStatusHistory(user);\n', indent=3)
            context.write('statusHistoryEl.innerHTML = this.renderStatusHistory(statusHistory);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Helper functions\n', indent=1)
            context.write('formatTimeDifference: function(timeDiff) {\n', indent=1)
            context.write('var seconds = Math.floor(timeDiff / 1000);\n', indent=2)
            context.write('var minutes = Math.floor(seconds / 60);\n', indent=2)
            context.write('var hours = Math.floor(minutes / 60);\n', indent=2)
            context.write('var days = Math.floor(hours / 24);\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (days > 0) return days + " days ago";\n', indent=2)
            context.write('if (hours > 0) return hours + " hours ago";\n', indent=2)
            context.write('if (minutes > 0) return minutes + " minutes ago";\n', indent=2)
            context.write('return "Just now";\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('estimateTimezone: function(user) {\n', indent=1)
            context.write('// Estimate timezone based on activity patterns\n', indent=2)
            context.write('if (!user.activityPattern) return null;\n', indent=2)
            context.write('// Implementation would analyze peak activity hours\n', indent=2)
            context.write('return "UTC-5 (estimated)";\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('getTypicalHours: function(user) {\n', indent=1)
            context.write('// Get typical online hours\n', indent=2)
            context.write('if (!user.activityPattern) return null;\n', indent=2)
            context.write('return "6PM - 11PM (estimated)";\n', indent=2)
            context.write('}\n', indent=1)
            context.write('};\n', indent=0)
            
        elif context.pattern == 'ENHANCED_AVATAR_CSS':
            context.write(context.line)
            context.write('\n/* Enhanced avatar card styles */\n')
            context.write('.enhanced-info-panel {\n')
            context.write('    margin-top: 15px;\n')
            context.write('    border-top: 1px solid #444;\n')
            context.write('    padding-top: 15px;\n')
            context.write('}\n\n')
            context.write('.info-tabs {\n')
            context.write('    display: flex;\n')
            context.write('    border-bottom: 1px solid #444;\n')
            context.write('    margin-bottom: 15px;\n')
            context.write('}\n\n')
            context.write('.tab-btn {\n')
            context.write('    background: transparent;\n')
            context.write('    border: none;\n')
            context.write('    color: #ccc;\n')
            context.write('    padding: 8px 16px;\n')
            context.write('    cursor: pointer;\n')
            context.write('    border-bottom: 2px solid transparent;\n')
            context.write('    transition: all 0.3s ease;\n')
            context.write('}\n\n')
            context.write('.tab-btn:hover {\n')
            context.write('    color: #fff;\n')
            context.write('    background: rgba(255, 255, 255, 0.1);\n')
            context.write('}\n\n')
            context.write('.tab-btn.active {\n')
            context.write('    color: #4CAF50;\n')
            context.write('    border-bottom-color: #4CAF50;\n')
            context.write('}\n\n')
            context.write('.tab-panel {\n')
            context.write('    display: none;\n')
            context.write('}\n\n')
            context.write('.tab-panel.active {\n')
            context.write('    display: block;\n')
            context.write('}\n\n')
            context.write('.info-row {\n')
            context.write('    display: flex;\n')
            context.write('    justify-content: space-between;\n')
            context.write('    margin-bottom: 8px;\n')
            context.write('    padding: 5px 0;\n')
            context.write('    border-bottom: 1px solid #333;\n')
            context.write('}\n\n')
            context.write('.info-row label {\n')
            context.write('    color: #aaa;\n')
            context.write('    font-weight: bold;\n')
            context.write('}\n\n')
            context.write('.info-row span {\n')
            context.write('    color: #fff;\n')
            context.write('}\n\n')
            context.write('.activity-chart {\n')
            context.write('    height: 150px;\n')
            context.write('    background: #222;\n')
            context.write('    border: 1px solid #444;\n')
            context.write('    margin-bottom: 15px;\n')
            context.write('    border-radius: 5px;\n')
            context.write('}\n\n')
            context.write('.activity-stats {\n')
            context.write('    display: grid;\n')
            context.write('    grid-template-columns: 1fr 1fr;\n')
            context.write('    gap: 10px;\n')
            context.write('}\n\n')
            context.write('.stat-item {\n')
            context.write('    background: #2a2a2a;\n')
            context.write('    padding: 10px;\n')
            context.write('    border-radius: 5px;\n')
            context.write('    border: 1px solid #444;\n')
            context.write('}\n\n')
            context.write('.stat-item label {\n')
            context.write('    display: block;\n')
            context.write('    color: #aaa;\n')
            context.write('    font-size: 12px;\n')
            context.write('    margin-bottom: 5px;\n')
            context.write('}\n\n')
            context.write('.stat-item span {\n')
            context.write('    color: #fff;\n')
            context.write('    font-weight: bold;\n')
            context.write('}\n')
