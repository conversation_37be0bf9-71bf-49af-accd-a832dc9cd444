from ...patch import InterfacePatch


class ActivityPatternAnalyzerPatch(InterfacePatch):
    """
    Analyzes user activity patterns to predict when users are likely to be online
    and help optimize timing for finding and contacting users.
    """
    def __init__(self):
        super(ActivityPatternAnalyzerPatch, self).__init__()
        
        # Add activity pattern analysis
        self.register('ACTIVITY_ANALYZER_JS', 'main/client.js', 'imvu.analyticsInit')
        self.register('PATTERN_DISPLAY_HTML', 'dialogs/activity_patterns/index.html', 'activity-analyzer-container')
        self.register('STATUS_TRACKING', 'dialogs/buddy_list/buddyList.js', 'onStatusUpdate')

    def patch(self, context):
        if context.pattern == 'ACTIVITY_ANALYZER_JS':
            context.write(context.line)
            context.write('\n// Activity Pattern Analyzer System\n')
            context.write('window.activityPatternAnalyzer = {\n', indent=0)
            context.write('userPatterns: {},\n', indent=1)
            context.write('globalPatterns: {},\n', indent=1)
            context.write('predictions: {},\n', indent=1)
            context.write('analysisCache: {},\n', indent=1)
            context.write('\n', indent=1)
            context.write('init: function() {\n', indent=1)
            context.write('this.loadFromStorage();\n', indent=2)
            context.write('this.startAnalysis();\n', indent=2)
            context.write('this.setupPeriodicAnalysis();\n', indent=2)
            context.write('console.log("Activity Pattern Analyzer initialized");\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Record user activity\n', indent=1)
            context.write('recordActivity: function(userId, activityType, timestamp) {\n', indent=1)
            context.write('timestamp = timestamp || new Date();\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (!this.userPatterns[userId]) {\n', indent=2)
            context.write('this.userPatterns[userId] = {\n', indent=3)
            context.write('hourlyActivity: new Array(24).fill(0),\n', indent=4)
            context.write('dailyActivity: new Array(7).fill(0),\n', indent=4)
            context.write('monthlyActivity: new Array(12).fill(0),\n', indent=4)
            context.write('statusChanges: [],\n', indent=4)
            context.write('sessionLengths: [],\n', indent=4)
            context.write('activityTypes: {},\n', indent=4)
            context.write('lastActivity: null,\n', indent=4)
            context.write('totalActiveDays: 0,\n', indent=4)
            context.write('averageSessionLength: 0,\n', indent=4)
            context.write('peakActivityHours: [],\n', indent=4)
            context.write('activityScore: 0\n', indent=4)
            context.write('};\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('var pattern = this.userPatterns[userId];\n', indent=2)
            context.write('var hour = timestamp.getHours();\n', indent=2)
            context.write('var day = timestamp.getDay();\n', indent=2)
            context.write('var month = timestamp.getMonth();\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Update activity counters\n', indent=2)
            context.write('pattern.hourlyActivity[hour]++;\n', indent=2)
            context.write('pattern.dailyActivity[day]++;\n', indent=2)
            context.write('pattern.monthlyActivity[month]++;\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Track activity types\n', indent=2)
            context.write('if (!pattern.activityTypes[activityType]) {\n', indent=2)
            context.write('pattern.activityTypes[activityType] = 0;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('pattern.activityTypes[activityType]++;\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Update last activity\n', indent=2)
            context.write('if (pattern.lastActivity) {\n', indent=2)
            context.write('var sessionLength = timestamp - new Date(pattern.lastActivity);\n', indent=3)
            context.write('if (sessionLength < 4 * 60 * 60 * 1000) { // Less than 4 hours = same session\n', indent=3)
            context.write('pattern.sessionLengths.push(sessionLength);\n', indent=4)
            context.write('}\n', indent=3)
            context.write('}\n', indent=2)
            context.write('pattern.lastActivity = timestamp.toISOString();\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Recalculate patterns\n', indent=2)
            context.write('this.calculateUserPatterns(userId);\n', indent=2)
            context.write('this.saveToStorage();\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Calculate user activity patterns\n', indent=1)
            context.write('calculateUserPatterns: function(userId) {\n', indent=1)
            context.write('var pattern = this.userPatterns[userId];\n', indent=2)
            context.write('if (!pattern) return;\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Calculate average session length\n', indent=2)
            context.write('if (pattern.sessionLengths.length > 0) {\n', indent=2)
            context.write('var totalTime = pattern.sessionLengths.reduce(function(sum, length) {\n', indent=3)
            context.write('return sum + length;\n', indent=4)
            context.write('}, 0);\n', indent=3)
            context.write('pattern.averageSessionLength = totalTime / pattern.sessionLengths.length;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Find peak activity hours\n', indent=2)
            context.write('var maxActivity = Math.max.apply(Math, pattern.hourlyActivity);\n', indent=2)
            context.write('var threshold = maxActivity * 0.7;\n', indent=2)
            context.write('pattern.peakActivityHours = [];\n', indent=2)
            context.write('\n', indent=2)
            context.write('for (var i = 0; i < 24; i++) {\n', indent=2)
            context.write('if (pattern.hourlyActivity[i] >= threshold) {\n', indent=3)
            context.write('pattern.peakActivityHours.push(i);\n', indent=4)
            context.write('}\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Calculate activity score\n', indent=2)
            context.write('var totalActivity = pattern.hourlyActivity.reduce(function(sum, count) {\n', indent=2)
            context.write('return sum + count;\n', indent=3)
            context.write('}, 0);\n', indent=2)
            context.write('var uniqueDays = pattern.dailyActivity.filter(function(count) {\n', indent=2)
            context.write('return count > 0;\n', indent=3)
            context.write('}).length;\n', indent=2)
            context.write('\n', indent=2)
            context.write('pattern.activityScore = (totalActivity * uniqueDays) / 7;\n', indent=2)
            context.write('pattern.totalActiveDays = uniqueDays;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Predict when user will be online\n', indent=1)
            context.write('predictUserActivity: function(userId, hoursAhead) {\n', indent=1)
            context.write('hoursAhead = hoursAhead || 24;\n', indent=2)
            context.write('var pattern = this.userPatterns[userId];\n', indent=2)
            context.write('if (!pattern) return [];\n', indent=2)
            context.write('\n', indent=2)
            context.write('var predictions = [];\n', indent=2)
            context.write('var now = new Date();\n', indent=2)
            context.write('\n', indent=2)
            context.write('for (var i = 0; i < hoursAhead; i++) {\n', indent=2)
            context.write('var futureTime = new Date(now.getTime() + i * 60 * 60 * 1000);\n', indent=3)
            context.write('var hour = futureTime.getHours();\n', indent=3)
            context.write('var day = futureTime.getDay();\n', indent=3)
            context.write('\n', indent=3)
            context.write('var hourlyScore = pattern.hourlyActivity[hour];\n', indent=3)
            context.write('var dailyScore = pattern.dailyActivity[day];\n', indent=3)
            context.write('var totalActivity = pattern.hourlyActivity.reduce(function(sum, count) {\n', indent=3)
            context.write('return sum + count;\n', indent=4)
            context.write('}, 0);\n', indent=3)
            context.write('\n', indent=3)
            context.write('var probability = 0;\n', indent=3)
            context.write('if (totalActivity > 0) {\n', indent=3)
            context.write('probability = (hourlyScore / totalActivity) * (dailyScore / totalActivity) * 100;\n', indent=4)
            context.write('}\n', indent=3)
            context.write('\n', indent=3)
            context.write('if (probability > 5) { // Only include meaningful predictions\n', indent=3)
            context.write('predictions.push({\n', indent=4)
            context.write('time: futureTime.toISOString(),\n', indent=5)
            context.write('hour: hour,\n', indent=5)
            context.write('day: day,\n', indent=5)
            context.write('probability: Math.min(probability, 95),\n', indent=5)
            context.write('confidence: this.calculateConfidence(pattern, hour, day)\n', indent=5)
            context.write('});\n', indent=4)
            context.write('}\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Sort by probability\n', indent=2)
            context.write('predictions.sort(function(a, b) { return b.probability - a.probability; });\n', indent=2)
            context.write('\n', indent=2)
            context.write('this.predictions[userId] = predictions;\n', indent=2)
            context.write('return predictions;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Calculate confidence in prediction\n', indent=1)
            context.write('calculateConfidence: function(pattern, hour, day) {\n', indent=1)
            context.write('var totalSamples = pattern.hourlyActivity.reduce(function(sum, count) {\n', indent=2)
            context.write('return sum + count;\n', indent=3)
            context.write('}, 0);\n', indent=2)
            context.write('\n', indent=2)
            context.write('var hourSamples = pattern.hourlyActivity[hour];\n', indent=2)
            context.write('var daySamples = pattern.dailyActivity[day];\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Confidence based on sample size\n', indent=2)
            context.write('var confidence = Math.min((hourSamples + daySamples) / Math.max(totalSamples / 10, 1), 1) * 100;\n', indent=2)
            context.write('return Math.round(confidence);\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Analyze global activity patterns\n', indent=1)
            context.write('analyzeGlobalPatterns: function() {\n', indent=1)
            context.write('var global = {\n', indent=2)
            context.write('hourlyActivity: new Array(24).fill(0),\n', indent=3)
            context.write('dailyActivity: new Array(7).fill(0),\n', indent=3)
            context.write('peakHours: [],\n', indent=3)
            context.write('totalUsers: 0,\n', indent=3)
            context.write('activeUsers: 0\n', indent=3)
            context.write('};\n', indent=2)
            context.write('\n', indent=2)
            context.write('Object.keys(this.userPatterns).forEach(function(userId) {\n', indent=2)
            context.write('var pattern = window.activityPatternAnalyzer.userPatterns[userId];\n', indent=3)
            context.write('global.totalUsers++;\n', indent=3)
            context.write('\n', indent=3)
            context.write('if (pattern.activityScore > 0) {\n', indent=3)
            context.write('global.activeUsers++;\n', indent=4)
            context.write('}\n', indent=3)
            context.write('\n', indent=3)
            context.write('for (var i = 0; i < 24; i++) {\n', indent=3)
            context.write('global.hourlyActivity[i] += pattern.hourlyActivity[i];\n', indent=4)
            context.write('}\n', indent=3)
            context.write('\n', indent=3)
            context.write('for (var j = 0; j < 7; j++) {\n', indent=3)
            context.write('global.dailyActivity[j] += pattern.dailyActivity[j];\n', indent=4)
            context.write('}\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Find global peak hours\n', indent=2)
            context.write('var maxGlobalActivity = Math.max.apply(Math, global.hourlyActivity);\n', indent=2)
            context.write('var globalThreshold = maxGlobalActivity * 0.8;\n', indent=2)
            context.write('\n', indent=2)
            context.write('for (var k = 0; k < 24; k++) {\n', indent=2)
            context.write('if (global.hourlyActivity[k] >= globalThreshold) {\n', indent=3)
            context.write('global.peakHours.push(k);\n', indent=4)
            context.write('}\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('this.globalPatterns = global;\n', indent=2)
            context.write('return global;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Get best times to find a user\n', indent=1)
            context.write('getBestContactTimes: function(userId, daysAhead) {\n', indent=1)
            context.write('daysAhead = daysAhead || 7;\n', indent=2)
            context.write('var predictions = this.predictUserActivity(userId, daysAhead * 24);\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Group by day and find best times\n', indent=2)
            context.write('var bestTimes = [];\n', indent=2)
            context.write('var dayGroups = {};\n', indent=2)
            context.write('\n', indent=2)
            context.write('predictions.forEach(function(pred) {\n', indent=2)
            context.write('var date = new Date(pred.time).toDateString();\n', indent=3)
            context.write('if (!dayGroups[date]) {\n', indent=3)
            context.write('dayGroups[date] = [];\n', indent=4)
            context.write('}\n', indent=3)
            context.write('dayGroups[date].push(pred);\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('Object.keys(dayGroups).forEach(function(date) {\n', indent=2)
            context.write('var dayPredictions = dayGroups[date];\n', indent=3)
            context.write('dayPredictions.sort(function(a, b) { return b.probability - a.probability; });\n', indent=3)
            context.write('\n', indent=3)
            context.write('// Take top 3 times for each day\n', indent=3)
            context.write('var topTimes = dayPredictions.slice(0, 3);\n', indent=3)
            context.write('bestTimes.push({\n', indent=3)
            context.write('date: date,\n', indent=4)
            context.write('times: topTimes\n', indent=4)
            context.write('});\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('return bestTimes;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Compare user patterns\n', indent=1)
            context.write('compareUserPatterns: function(userId1, userId2) {\n', indent=1)
            context.write('var pattern1 = this.userPatterns[userId1];\n', indent=2)
            context.write('var pattern2 = this.userPatterns[userId2];\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (!pattern1 || !pattern2) return null;\n', indent=2)
            context.write('\n', indent=2)
            context.write('var similarity = {\n', indent=2)
            context.write('hourly: this.calculateSimilarity(pattern1.hourlyActivity, pattern2.hourlyActivity),\n', indent=3)
            context.write('daily: this.calculateSimilarity(pattern1.dailyActivity, pattern2.dailyActivity),\n', indent=3)
            context.write('overall: 0\n', indent=3)
            context.write('};\n', indent=2)
            context.write('\n', indent=2)
            context.write('similarity.overall = (similarity.hourly + similarity.daily) / 2;\n', indent=2)
            context.write('\n', indent=2)
            context.write('return similarity;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Calculate similarity between two arrays\n', indent=1)
            context.write('calculateSimilarity: function(arr1, arr2) {\n', indent=1)
            context.write('if (arr1.length !== arr2.length) return 0;\n', indent=2)
            context.write('\n', indent=2)
            context.write('var sum1 = arr1.reduce(function(sum, val) { return sum + val; }, 0);\n', indent=2)
            context.write('var sum2 = arr2.reduce(function(sum, val) { return sum + val; }, 0);\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (sum1 === 0 || sum2 === 0) return 0;\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Normalize arrays\n', indent=2)
            context.write('var norm1 = arr1.map(function(val) { return val / sum1; });\n', indent=2)
            context.write('var norm2 = arr2.map(function(val) { return val / sum2; });\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Calculate cosine similarity\n', indent=2)
            context.write('var dotProduct = 0;\n', indent=2)
            context.write('var magnitude1 = 0;\n', indent=2)
            context.write('var magnitude2 = 0;\n', indent=2)
            context.write('\n', indent=2)
            context.write('for (var i = 0; i < norm1.length; i++) {\n', indent=2)
            context.write('dotProduct += norm1[i] * norm2[i];\n', indent=3)
            context.write('magnitude1 += norm1[i] * norm1[i];\n', indent=3)
            context.write('magnitude2 += norm2[i] * norm2[i];\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('magnitude1 = Math.sqrt(magnitude1);\n', indent=2)
            context.write('magnitude2 = Math.sqrt(magnitude2);\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (magnitude1 === 0 || magnitude2 === 0) return 0;\n', indent=2)
            context.write('\n', indent=2)
            context.write('return dotProduct / (magnitude1 * magnitude2);\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Start activity analysis\n', indent=1)
            context.write('startAnalysis: function() {\n', indent=1)
            context.write('// Hook into activity events\n', indent=2)
            context.write('if (window.imvu && window.imvu.events) {\n', indent=2)
            context.write('window.imvu.events.on("user.status.change", this.onStatusChange.bind(this));\n', indent=3)
            context.write('window.imvu.events.on("user.activity", this.onUserActivity.bind(this));\n', indent=3)
            context.write('window.imvu.events.on("room.enter", this.onRoomActivity.bind(this));\n', indent=3)
            context.write('window.imvu.events.on("message.sent", this.onMessageActivity.bind(this));\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Event handlers\n', indent=1)
            context.write('onStatusChange: function(userId, oldStatus, newStatus) {\n', indent=1)
            context.write('if (newStatus === "online" || newStatus === "away" || newStatus === "busy") {\n', indent=2)
            context.write('this.recordActivity(userId, "status_change", new Date());\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('onUserActivity: function(userId, activityType) {\n', indent=1)
            context.write('this.recordActivity(userId, activityType, new Date());\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('onRoomActivity: function(userId, roomId) {\n', indent=1)
            context.write('this.recordActivity(userId, "room_activity", new Date());\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('onMessageActivity: function(userId) {\n', indent=1)
            context.write('this.recordActivity(userId, "message_activity", new Date());\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Storage functions\n', indent=1)
            context.write('loadFromStorage: function() {\n', indent=1)
            context.write('try {\n', indent=2)
            context.write('var stored = localStorage.getItem("activityPatternAnalyzer");\n', indent=3)
            context.write('if (stored) {\n', indent=3)
            context.write('var data = JSON.parse(stored);\n', indent=4)
            context.write('this.userPatterns = data.userPatterns || {};\n', indent=4)
            context.write('this.globalPatterns = data.globalPatterns || {};\n', indent=4)
            context.write('this.predictions = data.predictions || {};\n', indent=4)
            context.write('this.analysisCache = data.analysisCache || {};\n', indent=4)
            context.write('}\n', indent=3)
            context.write('} catch (e) {\n', indent=2)
            context.write('console.error("Failed to load activity pattern data:", e);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('saveToStorage: function() {\n', indent=1)
            context.write('try {\n', indent=2)
            context.write('var data = {\n', indent=3)
            context.write('userPatterns: this.userPatterns,\n', indent=4)
            context.write('globalPatterns: this.globalPatterns,\n', indent=4)
            context.write('predictions: this.predictions,\n', indent=4)
            context.write('analysisCache: this.analysisCache,\n', indent=4)
            context.write('lastSaved: new Date().toISOString()\n', indent=4)
            context.write('};\n', indent=3)
            context.write('localStorage.setItem("activityPatternAnalyzer", JSON.stringify(data));\n', indent=3)
            context.write('} catch (e) {\n', indent=2)
            context.write('console.error("Failed to save activity pattern data:", e);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('setupPeriodicAnalysis: function() {\n', indent=1)
            context.write('setInterval(function() {\n', indent=2)
            context.write('window.activityPatternAnalyzer.analyzeGlobalPatterns();\n', indent=3)
            context.write('window.activityPatternAnalyzer.saveToStorage();\n', indent=3)
            context.write('}, 600000); // Analyze every 10 minutes\n', indent=2)
            context.write('}\n', indent=1)
            context.write('};\n', indent=0)
            context.write('\n')
            context.write('// Initialize activity pattern analyzer\n')
            context.write('window.activityPatternAnalyzer.init();\n')
            
        elif context.pattern == 'PATTERN_DISPLAY_HTML':
            context.write('<div class="activity-analyzer-container">', indent=0)
            context.write('<div class="analyzer-header">', indent=1)
            context.write('<h2>Activity Pattern Analyzer</h2>', indent=2)
            context.write('<input type="text" id="pattern-user-search" placeholder="Search user for pattern analysis...">', indent=2)
            context.write('</div>', indent=1)
            context.write('<div class="pattern-visualization">', indent=1)
            context.write('<div class="hourly-pattern">', indent=2)
            context.write('<h3>Hourly Activity Pattern</h3>', indent=3)
            context.write('<canvas id="hourly-chart" width="400" height="200"></canvas>', indent=3)
            context.write('</div>', indent=2)
            context.write('<div class="daily-pattern">', indent=2)
            context.write('<h3>Daily Activity Pattern</h3>', indent=3)
            context.write('<canvas id="daily-chart" width="400" height="200"></canvas>', indent=3)
            context.write('</div>', indent=2)
            context.write('</div>', indent=1)
            context.write('<div class="predictions-panel">', indent=1)
            context.write('<h3>Activity Predictions</h3>', indent=2)
            context.write('<div id="activity-predictions"></div>', indent=2)
            context.write('</div>', indent=1)
            context.write('<div class="best-times-panel">', indent=1)
            context.write('<h3>Best Contact Times</h3>', indent=2)
            context.write('<div id="best-contact-times"></div>', indent=2)
            context.write('</div>', indent=1)
            context.write('</div>', indent=0)
            
        elif context.pattern == 'STATUS_TRACKING':
            context.write(context.line)
            context.write('\n// Track status updates for activity analysis\n')
            context.write('if (window.activityPatternAnalyzer) {\n', indent=0)
            context.write('window.activityPatternAnalyzer.recordActivity(userId, "status_update", new Date());\n', indent=1)
            context.write('}\n', indent=0)
