from ...patch import InterfacePatch


class UserHistoryTrackerPatch(InterfacePatch):
    """
    Tracks and stores user activity patterns, status changes, and interaction history
    for enhanced user discovery and analysis.
    """
    def __init__(self):
        super(UserHistoryTrackerPatch, self).__init__()
        
        # Add history tracking to main client
        self.register('HISTORY_TRACKER_JS', 'main/client.js', 'imvu.init')
        self.register('BUDDY_LIST_TRACKING', 'dialogs/buddy_list/buddyList.js', 'updateBuddyStatus')
        self.register('ROOM_TRACKING', 'chat_rooms/room.js', 'onUserJoin')
        self.register('MESSAGE_TRACKING', 'chat/message.js', 'sendMessage')

    def patch(self, context):
        if context.pattern == 'HISTORY_TRACKER_JS':
            context.write(context.line)
            context.write('\n// User History Tracking System\n')
            context.write('window.userHistoryTracker = {\n', indent=0)
            context.write('// Storage for user data\n', indent=1)
            context.write('userDatabase: {},\n', indent=1)
            context.write('activityPatterns: {},\n', indent=1)
            context.write('statusHistory: {},\n', indent=1)
            context.write('interactionHistory: {},\n', indent=1)
            context.write('roomHistory: {},\n', indent=1)
            context.write('messageHistory: {},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Initialize tracking system\n', indent=1)
            context.write('init: function() {\n', indent=1)
            context.write('this.loadFromStorage();\n', indent=2)
            context.write('this.startPeriodicSave();\n', indent=2)
            context.write('this.setupEventListeners();\n', indent=2)
            context.write('console.log("User History Tracker initialized");\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Track user status change\n', indent=1)
            context.write('trackStatusChange: function(userId, oldStatus, newStatus) {\n', indent=1)
            context.write('if (!this.statusHistory[userId]) {\n', indent=2)
            context.write('this.statusHistory[userId] = [];\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('var statusChange = {\n', indent=2)
            context.write('timestamp: new Date().toISOString(),\n', indent=3)
            context.write('oldStatus: oldStatus,\n', indent=3)
            context.write('newStatus: newStatus,\n', indent=3)
            context.write('duration: this.calculateStatusDuration(userId, oldStatus)\n', indent=3)
            context.write('};\n', indent=2)
            context.write('\n', indent=2)
            context.write('this.statusHistory[userId].push(statusChange);\n', indent=2)
            context.write('this.updateActivityPattern(userId, newStatus);\n', indent=2)
            context.write('this.saveToStorage();\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Track user room activity\n', indent=1)
            context.write('trackRoomActivity: function(userId, roomId, action) {\n', indent=1)
            context.write('if (!this.roomHistory[userId]) {\n', indent=2)
            context.write('this.roomHistory[userId] = [];\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('var roomActivity = {\n', indent=2)
            context.write('timestamp: new Date().toISOString(),\n', indent=3)
            context.write('roomId: roomId,\n', indent=3)
            context.write('action: action, // "join", "leave", "message"\n', indent=3)
            context.write('roomName: this.getRoomName(roomId)\n', indent=3)
            context.write('};\n', indent=2)
            context.write('\n', indent=2)
            context.write('this.roomHistory[userId].push(roomActivity);\n', indent=2)
            context.write('this.updateRoomPreferences(userId, roomId, action);\n', indent=2)
            context.write('this.saveToStorage();\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Track user interactions\n', indent=1)
            context.write('trackInteraction: function(userId, interactionType, data) {\n', indent=1)
            context.write('if (!this.interactionHistory[userId]) {\n', indent=2)
            context.write('this.interactionHistory[userId] = [];\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('var interaction = {\n', indent=2)
            context.write('timestamp: new Date().toISOString(),\n', indent=3)
            context.write('type: interactionType, // "message", "friend_request", "profile_view", etc.\n', indent=3)
            context.write('data: data\n', indent=3)
            context.write('};\n', indent=2)
            context.write('\n', indent=2)
            context.write('this.interactionHistory[userId].push(interaction);\n', indent=2)
            context.write('this.updateSocialScore(userId, interactionType);\n', indent=2)
            context.write('this.saveToStorage();\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Update activity patterns\n', indent=1)
            context.write('updateActivityPattern: function(userId, status) {\n', indent=1)
            context.write('if (!this.activityPatterns[userId]) {\n', indent=2)
            context.write('this.activityPatterns[userId] = {\n', indent=3)
            context.write('hourlyActivity: new Array(24).fill(0),\n', indent=4)
            context.write('dailyActivity: new Array(7).fill(0),\n', indent=4)
            context.write('totalSessions: 0,\n', indent=4)
            context.write('totalOnlineTime: 0,\n', indent=4)
            context.write('averageSessionLength: 0,\n', indent=4)
            context.write('peakHours: [],\n', indent=4)
            context.write('favoriteRooms: {},\n', indent=4)
            context.write('lastUpdated: new Date().toISOString()\n', indent=4)
            context.write('};\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('var pattern = this.activityPatterns[userId];\n', indent=2)
            context.write('var now = new Date();\n', indent=2)
            context.write('var hour = now.getHours();\n', indent=2)
            context.write('var day = now.getDay();\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (status === "online" || status === "away" || status === "busy") {\n', indent=2)
            context.write('pattern.hourlyActivity[hour]++;\n', indent=3)
            context.write('pattern.dailyActivity[day]++;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('pattern.lastUpdated = now.toISOString();\n', indent=2)
            context.write('this.calculatePeakHours(userId);\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Calculate peak activity hours\n', indent=1)
            context.write('calculatePeakHours: function(userId) {\n', indent=1)
            context.write('var pattern = this.activityPatterns[userId];\n', indent=2)
            context.write('if (!pattern) return;\n', indent=2)
            context.write('\n', indent=2)
            context.write('var maxActivity = Math.max.apply(Math, pattern.hourlyActivity);\n', indent=2)
            context.write('var threshold = maxActivity * 0.7; // 70% of peak activity\n', indent=2)
            context.write('\n', indent=2)
            context.write('pattern.peakHours = [];\n', indent=2)
            context.write('for (var i = 0; i < 24; i++) {\n', indent=2)
            context.write('if (pattern.hourlyActivity[i] >= threshold) {\n', indent=3)
            context.write('pattern.peakHours.push(i);\n', indent=4)
            context.write('}\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Update room preferences\n', indent=1)
            context.write('updateRoomPreferences: function(userId, roomId, action) {\n', indent=1)
            context.write('var pattern = this.activityPatterns[userId];\n', indent=2)
            context.write('if (!pattern) return;\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (!pattern.favoriteRooms[roomId]) {\n', indent=2)
            context.write('pattern.favoriteRooms[roomId] = {\n', indent=3)
            context.write('visits: 0,\n', indent=4)
            context.write('totalTime: 0,\n', indent=4)
            context.write('messages: 0,\n', indent=4)
            context.write('lastVisit: null,\n', indent=4)
            context.write('roomName: this.getRoomName(roomId)\n', indent=4)
            context.write('};\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('var room = pattern.favoriteRooms[roomId];\n', indent=2)
            context.write('if (action === "join") {\n', indent=2)
            context.write('room.visits++;\n', indent=3)
            context.write('room.lastVisit = new Date().toISOString();\n', indent=3)
            context.write('} else if (action === "message") {\n', indent=2)
            context.write('room.messages++;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Update social score\n', indent=1)
            context.write('updateSocialScore: function(userId, interactionType) {\n', indent=1)
            context.write('if (!this.userDatabase[userId]) {\n', indent=2)
            context.write('this.userDatabase[userId] = {\n', indent=3)
            context.write('socialScore: 0,\n', indent=4)
            context.write('friendsCount: 0,\n', indent=4)
            context.write('messagesReceived: 0,\n', indent=4)
            context.write('messagesSent: 0,\n', indent=4)
            context.write('profileViews: 0,\n', indent=4)
            context.write('roomsVisited: 0\n', indent=4)
            context.write('};\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('var user = this.userDatabase[userId];\n', indent=2)
            context.write('switch (interactionType) {\n', indent=2)
            context.write('case "message_received":\n', indent=3)
            context.write('user.messagesReceived++;\n', indent=4)
            context.write('user.socialScore += 1;\n', indent=4)
            context.write('break;\n', indent=4)
            context.write('case "message_sent":\n', indent=3)
            context.write('user.messagesSent++;\n', indent=4)
            context.write('user.socialScore += 0.5;\n', indent=4)
            context.write('break;\n', indent=4)
            context.write('case "friend_added":\n', indent=3)
            context.write('user.friendsCount++;\n', indent=4)
            context.write('user.socialScore += 5;\n', indent=4)
            context.write('break;\n', indent=4)
            context.write('case "profile_view":\n', indent=3)
            context.write('user.profileViews++;\n', indent=4)
            context.write('user.socialScore += 0.1;\n', indent=4)
            context.write('break;\n', indent=4)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Calculate status duration\n', indent=1)
            context.write('calculateStatusDuration: function(userId, status) {\n', indent=1)
            context.write('var history = this.statusHistory[userId];\n', indent=2)
            context.write('if (!history || history.length === 0) return 0;\n', indent=2)
            context.write('\n', indent=2)
            context.write('var lastEntry = history[history.length - 1];\n', indent=2)
            context.write('if (lastEntry.newStatus === status) {\n', indent=2)
            context.write('var now = new Date();\n', indent=3)
            context.write('var lastTime = new Date(lastEntry.timestamp);\n', indent=3)
            context.write('return now - lastTime;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('return 0;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Get room name\n', indent=1)
            context.write('getRoomName: function(roomId) {\n', indent=1)
            context.write('if (window.imvu && window.imvu.roomCache && window.imvu.roomCache[roomId]) {\n', indent=2)
            context.write('return window.imvu.roomCache[roomId].name;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('return "Unknown Room";\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Load data from localStorage\n', indent=1)
            context.write('loadFromStorage: function() {\n', indent=1)
            context.write('try {\n', indent=2)
            context.write('var stored = localStorage.getItem("userHistoryTracker");\n', indent=3)
            context.write('if (stored) {\n', indent=3)
            context.write('var data = JSON.parse(stored);\n', indent=4)
            context.write('this.userDatabase = data.userDatabase || {};\n', indent=4)
            context.write('this.activityPatterns = data.activityPatterns || {};\n', indent=4)
            context.write('this.statusHistory = data.statusHistory || {};\n', indent=4)
            context.write('this.interactionHistory = data.interactionHistory || {};\n', indent=4)
            context.write('this.roomHistory = data.roomHistory || {};\n', indent=4)
            context.write('this.messageHistory = data.messageHistory || {};\n', indent=4)
            context.write('}\n', indent=3)
            context.write('} catch (e) {\n', indent=2)
            context.write('console.error("Failed to load user history data:", e);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Save data to localStorage\n', indent=1)
            context.write('saveToStorage: function() {\n', indent=1)
            context.write('try {\n', indent=2)
            context.write('var data = {\n', indent=3)
            context.write('userDatabase: this.userDatabase,\n', indent=4)
            context.write('activityPatterns: this.activityPatterns,\n', indent=4)
            context.write('statusHistory: this.statusHistory,\n', indent=4)
            context.write('interactionHistory: this.interactionHistory,\n', indent=4)
            context.write('roomHistory: this.roomHistory,\n', indent=4)
            context.write('messageHistory: this.messageHistory,\n', indent=4)
            context.write('lastSaved: new Date().toISOString()\n', indent=4)
            context.write('};\n', indent=3)
            context.write('localStorage.setItem("userHistoryTracker", JSON.stringify(data));\n', indent=3)
            context.write('} catch (e) {\n', indent=2)
            context.write('console.error("Failed to save user history data:", e);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Start periodic save\n', indent=1)
            context.write('startPeriodicSave: function() {\n', indent=1)
            context.write('setInterval(function() {\n', indent=2)
            context.write('window.userHistoryTracker.saveToStorage();\n', indent=3)
            context.write('}, 60000); // Save every minute\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Setup event listeners\n', indent=1)
            context.write('setupEventListeners: function() {\n', indent=1)
            context.write('// Listen for page unload to save data\n', indent=2)
            context.write('window.addEventListener("beforeunload", function() {\n', indent=2)
            context.write('window.userHistoryTracker.saveToStorage();\n', indent=3)
            context.write('});\n', indent=2)
            context.write('}\n', indent=1)
            context.write('};\n', indent=0)
            context.write('\n')
            context.write('// Initialize the tracker\n')
            context.write('window.userHistoryTracker.init();\n')
            
        elif context.pattern == 'BUDDY_LIST_TRACKING':
            context.write(context.line)
            context.write('\n// Track buddy status changes\n')
            context.write('if (window.userHistoryTracker && oldStatus !== newStatus) {\n', indent=0)
            context.write('window.userHistoryTracker.trackStatusChange(userId, oldStatus, newStatus);\n', indent=1)
            context.write('}\n', indent=0)
            
        elif context.pattern == 'ROOM_TRACKING':
            context.write(context.line)
            context.write('\n// Track room activity\n')
            context.write('if (window.userHistoryTracker) {\n', indent=0)
            context.write('window.userHistoryTracker.trackRoomActivity(userId, roomId, "join");\n', indent=1)
            context.write('}\n', indent=0)
            
        elif context.pattern == 'MESSAGE_TRACKING':
            context.write(context.line)
            context.write('\n// Track message interactions\n')
            context.write('if (window.userHistoryTracker) {\n', indent=0)
            context.write('window.userHistoryTracker.trackInteraction(recipientId, "message_received", {\n', indent=1)
            context.write('messageId: messageId,\n', indent=2)
            context.write('senderId: senderId,\n', indent=2)
            context.write('messageLength: message.length,\n', indent=2)
            context.write('roomId: currentRoomId\n', indent=2)
            context.write('});\n', indent=1)
            context.write('window.userHistoryTracker.trackInteraction(senderId, "message_sent", {\n', indent=1)
            context.write('messageId: messageId,\n', indent=2)
            context.write('recipientId: recipientId,\n', indent=2)
            context.write('messageLength: message.length,\n', indent=2)
            context.write('roomId: currentRoomId\n', indent=2)
            context.write('});\n', indent=1)
            context.write('}\n', indent=0)
