from ...patch import InterfacePatch


class AdvancedFriendSearchPatch(InterfacePatch):
    """
    Enhances the friend search functionality with advanced search capabilities
    including partial username search, CID search, and enhanced filtering options.
    """
    def __init__(self):
        super(AdvancedFriendSearchPatch, self).__init__()
        
        # Add advanced search interface to friends dialog
        self.register('FRIENDS_SEARCH_HTML', 'dialogs/friends/index.html', 'search-input')
        self.register('FRIENDS_SEARCH_JS', 'dialogs/friends/friends.js', 'searchFriends')
        self.register('FRIENDS_SEARCH_CSS', 'dialogs/friends/friends.css', 'search-input')
        
        # Enhance buddy list search
        self.register('BUDDY_LIST_HTML', 'dialogs/buddy_list/index.html', 'buddy-search')
        self.register('BUDDY_LIST_JS', 'dialogs/buddy_list/buddyList.js', 'filterBuddies')
        
        # Add global user search functionality
        self.register('GLOBA<PERSON>_SEARCH_HTML', 'dialogs/global_user_search/index.html', 'user-search-container')
        self.register('GLOBAL_SEARCH_JS', 'dialogs/global_user_search/globalUserSearch.js', 'searchUsers')

    def patch(self, context):
        if context.pattern == 'FRIENDS_SEARCH_HTML':
            context.write(context.line)
            context.write('<div class="advanced-search-panel">', indent=4)
            context.write('<div class="search-options">', indent=5)
            context.write('<label><input type="checkbox" id="search-by-cid"> Search by CID</label>', indent=6)
            context.write('<label><input type="checkbox" id="search-partial"> Partial match</label>', indent=6)
            context.write('<label><input type="checkbox" id="search-offline"> Include offline</label>', indent=6)
            context.write('<label><input type="checkbox" id="search-invisible"> Include invisible</label>', indent=6)
            context.write('</div>', indent=5)
            context.write('<div class="search-filters">', indent=5)
            context.write('<select id="status-filter">', indent=6)
            context.write('<option value="all">All Status</option>', indent=7)
            context.write('<option value="online">Online</option>', indent=7)
            context.write('<option value="away">Away</option>', indent=7)
            context.write('<option value="busy">Busy</option>', indent=7)
            context.write('<option value="invisible">Invisible</option>', indent=7)
            context.write('<option value="offline">Offline</option>', indent=7)
            context.write('</select>', indent=6)
            context.write('<input type="text" id="last-seen-filter" placeholder="Last seen (days ago)">', indent=6)
            context.write('</div>', indent=5)
            context.write('</div>', indent=4)
            
        elif context.pattern == 'FRIENDS_SEARCH_JS':
            context.write(context.line)
            context.write('\n// Enhanced search functionality\n')
            context.write('function advancedSearchFriends(query, options) {\n', indent=0)
            context.write('var results = [];\n', indent=1)
            context.write('var searchByCid = options.searchByCid || false;\n', indent=1)
            context.write('var partialMatch = options.partialMatch || false;\n', indent=1)
            context.write('var includeOffline = options.includeOffline || false;\n', indent=1)
            context.write('var includeInvisible = options.includeInvisible || false;\n', indent=1)
            context.write('var statusFilter = options.statusFilter || "all";\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Iterate through all known users\n', indent=1)
            context.write('for (var userId in imvu.userCache) {\n', indent=1)
            context.write('var user = imvu.userCache[userId];\n', indent=2)
            context.write('var matches = false;\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Check search criteria\n', indent=2)
            context.write('if (searchByCid && user.cid && user.cid.toString().indexOf(query) !== -1) {\n', indent=2)
            context.write('matches = true;\n', indent=3)
            context.write('} else if (partialMatch && user.username && user.username.toLowerCase().indexOf(query.toLowerCase()) !== -1) {\n', indent=2)
            context.write('matches = true;\n', indent=3)
            context.write('} else if (!partialMatch && user.username && user.username.toLowerCase() === query.toLowerCase()) {\n', indent=2)
            context.write('matches = true;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Apply status filters\n', indent=2)
            context.write('if (matches) {\n', indent=2)
            context.write('var userStatus = getUserStatus(user);\n', indent=3)
            context.write('if (statusFilter !== "all" && userStatus !== statusFilter) {\n', indent=3)
            context.write('matches = false;\n', indent=4)
            context.write('}\n', indent=3)
            context.write('if (!includeOffline && userStatus === "offline") {\n', indent=3)
            context.write('matches = false;\n', indent=4)
            context.write('}\n', indent=3)
            context.write('if (!includeInvisible && userStatus === "invisible") {\n', indent=3)
            context.write('matches = false;\n', indent=4)
            context.write('}\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (matches) {\n', indent=2)
            context.write('results.push(user);\n', indent=3)
            context.write('}\n', indent=2)
            context.write('}\n', indent=1)
            context.write('\n', indent=1)
            context.write('return results;\n', indent=1)
            context.write('}\n', indent=0)
            context.write('\n')
            context.write('// Helper function to determine user status\n')
            context.write('function getUserStatus(user) {\n', indent=0)
            context.write('if (!user.lastSeen) return "unknown";\n', indent=1)
            context.write('var now = new Date().getTime();\n', indent=1)
            context.write('var lastSeen = new Date(user.lastSeen).getTime();\n', indent=1)
            context.write('var timeDiff = now - lastSeen;\n', indent=1)
            context.write('var minutesDiff = timeDiff / (1000 * 60);\n', indent=1)
            context.write('\n', indent=1)
            context.write('if (user.status === "invisible") return "invisible";\n', indent=1)
            context.write('if (minutesDiff < 5) return "online";\n', indent=1)
            context.write('if (minutesDiff < 30) return "away";\n', indent=1)
            context.write('if (user.status === "busy") return "busy";\n', indent=1)
            context.write('return "offline";\n', indent=1)
            context.write('}\n', indent=0)
            
        elif context.pattern == 'FRIENDS_SEARCH_CSS':
            context.write(context.line)
            context.write('\n/* Advanced search panel styles */\n')
            context.write('.advanced-search-panel {\n')
            context.write('    background: #2a2a2a;\n')
            context.write('    border: 1px solid #444;\n')
            context.write('    border-radius: 5px;\n')
            context.write('    padding: 10px;\n')
            context.write('    margin-top: 10px;\n')
            context.write('}\n\n')
            context.write('.search-options {\n')
            context.write('    display: flex;\n')
            context.write('    flex-wrap: wrap;\n')
            context.write('    gap: 10px;\n')
            context.write('    margin-bottom: 10px;\n')
            context.write('}\n\n')
            context.write('.search-options label {\n')
            context.write('    color: #fff;\n')
            context.write('    font-size: 12px;\n')
            context.write('    cursor: pointer;\n')
            context.write('}\n\n')
            context.write('.search-filters {\n')
            context.write('    display: flex;\n')
            context.write('    gap: 10px;\n')
            context.write('    align-items: center;\n')
            context.write('}\n\n')
            context.write('.search-filters select,\n')
            context.write('.search-filters input {\n')
            context.write('    background: #333;\n')
            context.write('    border: 1px solid #555;\n')
            context.write('    color: #fff;\n')
            context.write('    padding: 5px;\n')
            context.write('    border-radius: 3px;\n')
            context.write('}\n')
            
        elif context.pattern == 'BUDDY_LIST_HTML':
            context.write(context.line)
            context.write('<div class="enhanced-buddy-search">', indent=3)
            context.write('<input type="text" id="enhanced-buddy-search" placeholder="Search by name, CID, or status...">', indent=4)
            context.write('<div class="buddy-search-results" id="buddy-search-results"></div>', indent=4)
            context.write('</div>', indent=3)
            
        elif context.pattern == 'BUDDY_LIST_JS':
            context.write(context.line)
            context.write('\n// Enhanced buddy filtering\n')
            context.write('function enhancedFilterBuddies(query) {\n', indent=0)
            context.write('var results = [];\n', indent=1)
            context.write('var buddyList = document.getElementById("buddy-list");\n', indent=1)
            context.write('var buddyItems = buddyList.getElementsByClassName("buddy-item");\n', indent=1)
            context.write('\n', indent=1)
            context.write('for (var i = 0; i < buddyItems.length; i++) {\n', indent=1)
            context.write('var buddy = buddyItems[i];\n', indent=2)
            context.write('var username = buddy.getAttribute("data-username") || "";\n', indent=2)
            context.write('var cid = buddy.getAttribute("data-cid") || "";\n', indent=2)
            context.write('var status = buddy.getAttribute("data-status") || "";\n', indent=2)
            context.write('\n', indent=2)
            context.write('var searchText = query.toLowerCase();\n', indent=2)
            context.write('var matches = username.toLowerCase().indexOf(searchText) !== -1 ||\n', indent=2)
            context.write('             cid.indexOf(searchText) !== -1 ||\n', indent=2)
            context.write('             status.toLowerCase().indexOf(searchText) !== -1;\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (matches) {\n', indent=2)
            context.write('buddy.style.display = "block";\n', indent=3)
            context.write('results.push(buddy);\n', indent=3)
            context.write('} else {\n', indent=2)
            context.write('buddy.style.display = "none";\n', indent=3)
            context.write('}\n', indent=2)
            context.write('}\n', indent=1)
            context.write('\n', indent=1)
            context.write('return results;\n', indent=1)
            context.write('}\n', indent=0)
            
        elif context.pattern == 'GLOBAL_SEARCH_HTML':
            context.write('<div class="global-user-search-container">', indent=0)
            context.write('<div class="search-header">', indent=1)
            context.write('<h2>Global User Search</h2>', indent=2)
            context.write('<input type="text" id="global-search-input" placeholder="Search users globally...">', indent=2)
            context.write('<button id="global-search-btn">Search</button>', indent=2)
            context.write('</div>', indent=1)
            context.write('<div class="search-results" id="global-search-results"></div>', indent=1)
            context.write('</div>', indent=0)
            
        elif context.pattern == 'GLOBAL_SEARCH_JS':
            context.write('function globalUserSearch(query, options) {\n', indent=0)
            context.write('// This function performs a global search across all known users\n', indent=1)
            context.write('var searchOptions = options || {};\n', indent=1)
            context.write('var results = [];\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Search through cached users\n', indent=1)
            context.write('if (window.imvu && window.imvu.userCache) {\n', indent=1)
            context.write('results = results.concat(searchCachedUsers(query, searchOptions));\n', indent=2)
            context.write('}\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Search through room histories\n', indent=1)
            context.write('if (window.roomHistory) {\n', indent=1)
            context.write('results = results.concat(searchRoomHistory(query, searchOptions));\n', indent=2)
            context.write('}\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Search through friend lists\n', indent=1)
            context.write('if (window.friendsCache) {\n', indent=1)
            context.write('results = results.concat(searchFriendsCache(query, searchOptions));\n', indent=2)
            context.write('}\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Remove duplicates and sort by relevance\n', indent=1)
            context.write('results = removeDuplicateUsers(results);\n', indent=1)
            context.write('results = sortUsersByRelevance(results, query);\n', indent=1)
            context.write('\n', indent=1)
            context.write('return results;\n', indent=1)
            context.write('}\n', indent=0)
