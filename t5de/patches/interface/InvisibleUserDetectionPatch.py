from ...patch import InterfacePatch


class InvisibleUserDetectionPatch(InterfacePatch):
    """
    Detects users who appear offline but are actually invisible or online.
    Uses various techniques to identify hidden online status.
    """
    def __init__(self):
        super(InvisibleUserDetectionPatch, self).__init__()
        
        # Patch buddy list to show invisible detection
        self.register('BUDDY_LIST_INVISIBLE_HTML', 'dialogs/buddy_list/index.html', 'buddy-status')
        self.register('BUDDY_LIST_INVISIBLE_JS', 'dialogs/buddy_list/buddyList.js', 'updateBuddyStatus')
        self.register('BUDDY_LIST_INVISIBLE_CSS', 'dialogs/buddy_list/buddyList.css', 'buddy-status')
        
        # Patch room participant lists
        self.register('ROOM_PARTICIPANTS_JS', 'chat_rooms/participants.js', 'updateParticipants')
        
        # Patch avatar cards to show detection info
        self.register('AVATAR_CARD_INVISIBLE_HTML', 'dialogs/avatar_card/index.html', 'status-info')
        self.register('AVATAR_CARD_INVISIBLE_JS', 'dialogs/avatar_card/avatarCard.js', 'updateStatusInfo')

    def patch(self, context):
        if context.pattern == 'BUDDY_LIST_INVISIBLE_HTML':
            context.write(context.line)
            context.write('<div class="invisible-detection-indicator" id="invisible-indicator">', indent=4)
            context.write('<span class="detection-status" title="Invisible Detection Status"></span>', indent=5)
            context.write('<span class="detection-confidence"></span>', indent=5)
            context.write('</div>', indent=4)
            
        elif context.pattern == 'BUDDY_LIST_INVISIBLE_JS':
            context.write(context.line)
            context.write('\n// Invisible user detection system\n')
            context.write('var invisibleDetection = {\n', indent=0)
            context.write('cache: {},\n', indent=1)
            context.write('lastActivity: {},\n', indent=1)
            context.write('patterns: {},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Detect invisible users through various methods\n', indent=1)
            context.write('detectInvisible: function(userId) {\n', indent=1)
            context.write('var user = imvu.userCache[userId];\n', indent=2)
            context.write('if (!user) return null;\n', indent=2)
            context.write('\n', indent=2)
            context.write('var detection = {\n', indent=2)
            context.write('userId: userId,\n', indent=3)
            context.write('confidence: 0,\n', indent=3)
            context.write('methods: [],\n', indent=3)
            context.write('lastSeen: user.lastSeen,\n', indent=3)
            context.write('actualStatus: "unknown"\n', indent=3)
            context.write('};\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Method 1: Check for recent activity patterns\n', indent=2)
            context.write('if (this.checkActivityPattern(userId)) {\n', indent=2)
            context.write('detection.confidence += 30;\n', indent=3)
            context.write('detection.methods.push("activity_pattern");\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Method 2: Check for avatar updates while "offline"\n', indent=2)
            context.write('if (this.checkAvatarUpdates(userId)) {\n', indent=2)
            context.write('detection.confidence += 25;\n', indent=3)
            context.write('detection.methods.push("avatar_updates");\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Method 3: Check for room presence inconsistencies\n', indent=2)
            context.write('if (this.checkRoomPresence(userId)) {\n', indent=2)
            context.write('detection.confidence += 20;\n', indent=3)
            context.write('detection.methods.push("room_presence");\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Method 4: Check for message delivery patterns\n', indent=2)
            context.write('if (this.checkMessagePatterns(userId)) {\n', indent=2)
            context.write('detection.confidence += 15;\n', indent=3)
            context.write('detection.methods.push("message_patterns");\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Method 5: Check for friend list updates\n', indent=2)
            context.write('if (this.checkFriendListActivity(userId)) {\n', indent=2)
            context.write('detection.confidence += 10;\n', indent=3)
            context.write('detection.methods.push("friend_activity");\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Determine likely actual status\n', indent=2)
            context.write('if (detection.confidence >= 70) {\n', indent=2)
            context.write('detection.actualStatus = "likely_invisible";\n', indent=3)
            context.write('} else if (detection.confidence >= 40) {\n', indent=2)
            context.write('detection.actualStatus = "possibly_invisible";\n', indent=3)
            context.write('} else if (detection.confidence >= 20) {\n', indent=2)
            context.write('detection.actualStatus = "recently_active";\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('this.cache[userId] = detection;\n', indent=2)
            context.write('return detection;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Check for consistent activity patterns\n', indent=1)
            context.write('checkActivityPattern: function(userId) {\n', indent=1)
            context.write('var pattern = this.patterns[userId];\n', indent=2)
            context.write('if (!pattern) return false;\n', indent=2)
            context.write('\n', indent=2)
            context.write('var now = new Date();\n', indent=2)
            context.write('var currentHour = now.getHours();\n', indent=2)
            context.write('var dayOfWeek = now.getDay();\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Check if user typically online at this time\n', indent=2)
            context.write('if (pattern.typicalHours && pattern.typicalHours[currentHour] > 0.7) {\n', indent=2)
            context.write('return true;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Check if user typically online on this day\n', indent=2)
            context.write('if (pattern.typicalDays && pattern.typicalDays[dayOfWeek] > 0.7) {\n', indent=2)
            context.write('return true;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('return false;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Check for avatar updates while appearing offline\n', indent=1)
            context.write('checkAvatarUpdates: function(userId) {\n', indent=1)
            context.write('var user = imvu.userCache[userId];\n', indent=2)
            context.write('if (!user || !user.avatar) return false;\n', indent=2)
            context.write('\n', indent=2)
            context.write('var lastUpdate = user.avatar.lastModified;\n', indent=2)
            context.write('var lastSeen = user.lastSeen;\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (lastUpdate && lastSeen) {\n', indent=2)
            context.write('var updateTime = new Date(lastUpdate).getTime();\n', indent=3)
            context.write('var seenTime = new Date(lastSeen).getTime();\n', indent=3)
            context.write('// Avatar updated after last seen time indicates invisible activity\n', indent=3)
            context.write('return updateTime > seenTime;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('return false;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Check for room presence inconsistencies\n', indent=1)
            context.write('checkRoomPresence: function(userId) {\n', indent=1)
            context.write('// Check if user appears in rooms while showing offline\n', indent=2)
            context.write('if (window.roomParticipants) {\n', indent=2)
            context.write('for (var roomId in window.roomParticipants) {\n', indent=3)
            context.write('var participants = window.roomParticipants[roomId];\n', indent=4)
            context.write('if (participants.indexOf(userId) !== -1) {\n', indent=4)
            context.write('var user = imvu.userCache[userId];\n', indent=5)
            context.write('if (user && user.status === "offline") {\n', indent=5)
            context.write('return true; // User in room but showing offline\n', indent=6)
            context.write('}\n', indent=5)
            context.write('}\n', indent=4)
            context.write('}\n', indent=3)
            context.write('}\n', indent=2)
            context.write('return false;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Check message delivery and read patterns\n', indent=1)
            context.write('checkMessagePatterns: function(userId) {\n', indent=1)
            context.write('// Check if messages are being read quickly despite offline status\n', indent=2)
            context.write('var messageHistory = this.getMessageHistory(userId);\n', indent=2)
            context.write('if (!messageHistory) return false;\n', indent=2)
            context.write('\n', indent=2)
            context.write('var recentMessages = messageHistory.filter(function(msg) {\n', indent=2)
            context.write('return (new Date() - new Date(msg.timestamp)) < (30 * 60 * 1000); // 30 minutes\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('var quickReads = recentMessages.filter(function(msg) {\n', indent=2)
            context.write('return msg.readTime && (new Date(msg.readTime) - new Date(msg.timestamp)) < (5 * 60 * 1000); // Read within 5 minutes\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('return quickReads.length > 0;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Check for friend list activity\n', indent=1)
            context.write('checkFriendListActivity: function(userId) {\n', indent=1)
            context.write('// Check if user is adding/removing friends while offline\n', indent=2)
            context.write('var user = imvu.userCache[userId];\n', indent=2)
            context.write('if (!user || !user.friendsLastModified) return false;\n', indent=2)
            context.write('\n', indent=2)
            context.write('var lastModified = new Date(user.friendsLastModified).getTime();\n', indent=2)
            context.write('var lastSeen = new Date(user.lastSeen).getTime();\n', indent=2)
            context.write('var timeDiff = lastModified - lastSeen;\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Friend list modified after last seen time\n', indent=2)
            context.write('return timeDiff > 0 && timeDiff < (24 * 60 * 60 * 1000); // Within 24 hours\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Get message history for user\n', indent=1)
            context.write('getMessageHistory: function(userId) {\n', indent=1)
            context.write('if (window.messageCache && window.messageCache[userId]) {\n', indent=2)
            context.write('return window.messageCache[userId];\n', indent=3)
            context.write('}\n', indent=2)
            context.write('return null;\n', indent=2)
            context.write('}\n', indent=1)
            context.write('};\n', indent=0)
            
        elif context.pattern == 'BUDDY_LIST_INVISIBLE_CSS':
            context.write(context.line)
            context.write('\n/* Invisible detection indicator styles */\n')
            context.write('.invisible-detection-indicator {\n')
            context.write('    display: inline-flex;\n')
            context.write('    align-items: center;\n')
            context.write('    margin-left: 5px;\n')
            context.write('}\n\n')
            context.write('.detection-status {\n')
            context.write('    width: 8px;\n')
            context.write('    height: 8px;\n')
            context.write('    border-radius: 50%;\n')
            context.write('    margin-right: 3px;\n')
            context.write('}\n\n')
            context.write('.detection-status.likely-invisible {\n')
            context.write('    background-color: #ff4444;\n')
            context.write('    box-shadow: 0 0 4px #ff4444;\n')
            context.write('}\n\n')
            context.write('.detection-status.possibly-invisible {\n')
            context.write('    background-color: #ffaa00;\n')
            context.write('    box-shadow: 0 0 4px #ffaa00;\n')
            context.write('}\n\n')
            context.write('.detection-status.recently-active {\n')
            context.write('    background-color: #44ff44;\n')
            context.write('    box-shadow: 0 0 4px #44ff44;\n')
            context.write('}\n\n')
            context.write('.detection-confidence {\n')
            context.write('    font-size: 10px;\n')
            context.write('    color: #ccc;\n')
            context.write('    font-weight: bold;\n')
            context.write('}\n')
            
        elif context.pattern == 'ROOM_PARTICIPANTS_JS':
            context.write(context.line)
            context.write('\n// Enhanced participant tracking for invisible detection\n')
            context.write('function trackRoomParticipants(roomId, participants) {\n', indent=0)
            context.write('if (!window.roomParticipants) {\n', indent=1)
            context.write('window.roomParticipants = {};\n', indent=2)
            context.write('}\n', indent=1)
            context.write('\n', indent=1)
            context.write('window.roomParticipants[roomId] = participants;\n', indent=1)
            context.write('\n', indent=1)
            context.write('// Check for invisible users in room\n', indent=1)
            context.write('participants.forEach(function(userId) {\n', indent=1)
            context.write('var user = imvu.userCache[userId];\n', indent=2)
            context.write('if (user && user.status === "offline") {\n', indent=2)
            context.write('// User appears offline but is in room - likely invisible\n', indent=3)
            context.write('console.log("Detected potentially invisible user in room:", userId, user.username);\n', indent=3)
            context.write('if (window.invisibleDetection) {\n', indent=3)
            context.write('window.invisibleDetection.detectInvisible(userId);\n', indent=4)
            context.write('}\n', indent=3)
            context.write('}\n', indent=2)
            context.write('});\n', indent=1)
            context.write('}\n', indent=0)
            
        elif context.pattern == 'AVATAR_CARD_INVISIBLE_HTML':
            context.write(context.line)
            context.write('<div class="invisible-detection-info">', indent=6)
            context.write('<div class="detection-header">Invisible Detection</div>', indent=7)
            context.write('<div class="detection-details" id="detection-details"></div>', indent=7)
            context.write('</div>', indent=6)
            
        elif context.pattern == 'AVATAR_CARD_INVISIBLE_JS':
            context.write(context.line)
            context.write('\n// Update invisible detection info in avatar card\n')
            context.write('function updateInvisibleDetectionInfo(userId) {\n', indent=0)
            context.write('var detailsEl = document.getElementById("detection-details");\n', indent=1)
            context.write('if (!detailsEl) return;\n', indent=1)
            context.write('\n', indent=1)
            context.write('if (window.invisibleDetection && window.invisibleDetection.cache[userId]) {\n', indent=1)
            context.write('var detection = window.invisibleDetection.cache[userId];\n', indent=2)
            context.write('var html = "";\n', indent=2)
            context.write('\n', indent=2)
            context.write('html += "<div class=\\"detection-confidence\\">Confidence: " + detection.confidence + "%</div>";\n', indent=2)
            context.write('html += "<div class=\\"detection-status\\">Status: " + detection.actualStatus + "</div>";\n', indent=2)
            context.write('html += "<div class=\\"detection-methods\\">Methods: " + detection.methods.join(", ") + "</div>";\n', indent=2)
            context.write('\n', indent=2)
            context.write('detailsEl.innerHTML = html;\n', indent=2)
            context.write('} else {\n', indent=1)
            context.write('detailsEl.innerHTML = "<div>No detection data available</div>";\n', indent=2)
            context.write('}\n', indent=1)
            context.write('}\n', indent=0)
