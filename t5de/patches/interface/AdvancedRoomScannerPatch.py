from ...patch import InterfacePatch


class AdvancedRoomScannerPatch(InterfacePatch):
    """
    Advanced room scanning functionality to find specific users across all rooms,
    even when they're not visible in normal room lists.
    """
    def __init__(self):
        super(AdvancedRoomScannerPatch, self).__init__()

        # Add room scanner interface
        self.register('ROOM_SCANNER_HTML', 'dialogs/room_scanner/index.html', 'room-scanner-container')
        self.register('ROOM_SCANNER_JS', 'dialogs/room_scanner/roomScanner.js', 'initRoomScanner')
        self.register('ROOM_SCANNER_CSS', 'dialogs/room_scanner/roomScanner.css', 'room-scanner')

        # Enhance existing room search
        self.register('CHAT_ROOM_SEARCH_ENHANCED', 'chat_rooms/ChatRoomSearch.js', 'searchRooms')
        self.register('ROOM_LIST_ENHANCED', 'chat_rooms/RoomList.js', 'loadRooms')

    def patch(self, context):
        if context.pattern == 'ROOM_SCANNER_HTML':
            context.write('<div class="room-scanner-container">', indent=0)
            context.write('<div class="scanner-header">', indent=1)
            context.write('<h2>Advanced Room Scanner</h2>', indent=2)
            context.write('<div class="scanner-controls">', indent=2)
            context.write('<input type="text" id="target-user-search" placeholder="Search for specific user...">', indent=3)
            context.write('<button id="scan-all-rooms">Scan All Rooms</button>', indent=3)
            context.write('<button id="scan-favorites">Scan Favorites</button>', indent=3)
            context.write('<button id="deep-scan">Deep Scan</button>', indent=3)
            context.write('</div>', indent=2)
            context.write('</div>', indent=1)
            context.write('<div class="scanner-options">', indent=1)
            context.write('<div class="option-group">', indent=2)
            context.write('<label>Scan Options:</label>', indent=3)
            context.write('<label><input type="checkbox" id="include-private" checked> Include Private Rooms</label>', indent=3)
            context.write('<label><input type="checkbox" id="include-invisible" checked> Detect Invisible Users</label>', indent=3)
            context.write('<label><input type="checkbox" id="scan-history" checked> Use Room History</label>', indent=3)
            context.write('<label><input type="checkbox" id="continuous-scan"> Continuous Scanning</label>', indent=3)
            context.write('</div>', indent=2)
            context.write('<div class="option-group">', indent=2)
            context.write('<label>Filters:</label>', indent=3)
            context.write('<select id="room-category-filter">', indent=3)
            context.write('<option value="all">All Categories</option>', indent=4)
            context.write('<option value="social">Social</option>', indent=4)
            context.write('<option value="gaming">Gaming</option>', indent=4)
            context.write('<option value="music">Music</option>', indent=4)
            context.write('<option value="roleplay">Roleplay</option>', indent=4)
            context.write('</select>', indent=3)
            context.write('<input type="number" id="min-users" placeholder="Min users" min="0">', indent=3)
            context.write('<input type="number" id="max-users" placeholder="Max users" min="0">', indent=3)
            context.write('</div>', indent=2)
            context.write('</div>', indent=1)
            context.write('<div class="scan-progress">', indent=1)
            context.write('<div class="progress-bar">', indent=2)
            context.write('<div class="progress-fill" id="scan-progress-fill"></div>', indent=3)
            context.write('</div>', indent=2)
            context.write('<div class="progress-text" id="scan-progress-text">Ready to scan</div>', indent=2)
            context.write('</div>', indent=1)
            context.write('<div class="scan-results">', indent=1)
            context.write('<div class="results-header">', indent=2)
            context.write('<h3>Scan Results</h3>', indent=3)
            context.write('<div class="results-stats" id="results-stats"></div>', indent=3)
            context.write('</div>', indent=2)
            context.write('<div class="results-list" id="scan-results-list"></div>', indent=2)
            context.write('</div>', indent=1)
            context.write('</div>', indent=0)

        elif context.pattern == 'ROOM_SCANNER_JS':
            context.write('var advancedRoomScanner = {\n', indent=0)
            context.write('isScanning: false,\n', indent=1)
            context.write('scanResults: [],\n', indent=1)
            context.write('targetUsers: [],\n', indent=1)
            context.write('scannedRooms: new Set(),\n', indent=1)
            context.write('roomCache: {},\n', indent=1)
            context.write('\n', indent=1)
            context.write('init: function() {\n', indent=1)
            context.write('this.setupEventListeners();\n', indent=2)
            context.write('this.loadRoomCache();\n', indent=2)
            context.write('console.log("Advanced Room Scanner initialized");\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('setupEventListeners: function() {\n', indent=1)
            context.write('var self = this;\n', indent=2)
            context.write('\n', indent=2)
            context.write('document.getElementById("scan-all-rooms").addEventListener("click", function() {\n', indent=2)
            context.write('self.startScan("all");\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('document.getElementById("scan-favorites").addEventListener("click", function() {\n', indent=2)
            context.write('self.startScan("favorites");\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('document.getElementById("deep-scan").addEventListener("click", function() {\n', indent=2)
            context.write('self.startScan("deep");\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('document.getElementById("target-user-search").addEventListener("input", function() {\n', indent=2)
            context.write('self.updateTargetUsers(this.value);\n', indent=3)
            context.write('});\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('startScan: function(scanType) {\n', indent=1)
            context.write('if (this.isScanning) {\n', indent=2)
            context.write('this.stopScan();\n', indent=3)
            context.write('return;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('this.isScanning = true;\n', indent=2)
            context.write('this.scanResults = [];\n', indent=2)
            context.write('this.scannedRooms.clear();\n', indent=2)
            context.write('\n', indent=2)
            context.write('this.updateProgress(0, "Starting scan...");\n', indent=2)
            context.write('\n', indent=2)
            context.write('switch (scanType) {\n', indent=2)
            context.write('case "all":\n', indent=3)
            context.write('this.scanAllRooms();\n', indent=4)
            context.write('break;\n', indent=4)
            context.write('case "favorites":\n', indent=3)
            context.write('this.scanFavoriteRooms();\n', indent=4)
            context.write('break;\n', indent=4)
            context.write('case "deep":\n', indent=3)
            context.write('this.deepScanRooms();\n', indent=4)
            context.write('break;\n', indent=4)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('scanAllRooms: function() {\n', indent=1)
            context.write('var self = this;\n', indent=2)
            context.write('var roomList = this.getAllRoomList();\n', indent=2)
            context.write('var totalRooms = roomList.length;\n', indent=2)
            context.write('var scannedCount = 0;\n', indent=2)
            context.write('\n', indent=2)
            context.write('function scanNextRoom() {\n', indent=2)
            context.write('if (!self.isScanning || scannedCount >= totalRooms) {\n', indent=3)
            context.write('self.completeScan();\n', indent=4)
            context.write('return;\n', indent=4)
            context.write('}\n', indent=3)
            context.write('\n', indent=3)
            context.write('var room = roomList[scannedCount];\n', indent=3)
            context.write('self.updateProgress((scannedCount / totalRooms) * 100, "Scanning: " + room.name);\n', indent=3)
            context.write('\n', indent=3)
            context.write('self.scanRoom(room).then(function(result) {\n', indent=3)
            context.write('if (result.foundUsers.length > 0) {\n', indent=4)
            context.write('self.scanResults.push(result);\n', indent=5)
            context.write('self.updateResultsDisplay();\n', indent=5)
            context.write('}\n', indent=4)
            context.write('scannedCount++;\n', indent=4)
            context.write('setTimeout(scanNextRoom, 100); // Throttle requests\n', indent=4)
            context.write('}).catch(function(error) {\n', indent=3)
            context.write('console.error("Error scanning room:", room.name, error);\n', indent=4)
            context.write('scannedCount++;\n', indent=4)
            context.write('setTimeout(scanNextRoom, 100);\n', indent=4)
            context.write('});\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('scanNextRoom();\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('scanRoom: function(room) {\n', indent=1)
            context.write('var self = this;\n', indent=2)
            context.write('return new Promise(function(resolve, reject) {\n', indent=2)
            context.write('// Attempt to get room participant list\n', indent=3)
            context.write('self.getRoomParticipants(room.id).then(function(participants) {\n', indent=3)
            context.write('var foundUsers = [];\n', indent=4)
            context.write('\n', indent=4)
            context.write('// Check each participant against target users\n', indent=4)
            context.write('participants.forEach(function(participant) {\n', indent=4)
            context.write('if (self.isTargetUser(participant)) {\n', indent=5)
            context.write('foundUsers.push({\n', indent=6)
            context.write('userId: participant.id,\n', indent=7)
            context.write('username: participant.username,\n', indent=7)
            context.write('status: participant.status,\n', indent=7)
            context.write('isInvisible: self.detectInvisibleStatus(participant),\n', indent=7)
            context.write('lastSeen: participant.lastSeen\n', indent=7)
            context.write('});\n', indent=6)
            context.write('}\n', indent=5)
            context.write('});\n', indent=4)
            context.write('\n', indent=4)
            context.write('// Also check for invisible users using advanced detection\n', indent=4)
            context.write('if (document.getElementById("include-invisible").checked) {\n', indent=4)
            context.write('var invisibleUsers = self.detectInvisibleUsers(room);\n', indent=5)
            context.write('foundUsers = foundUsers.concat(invisibleUsers);\n', indent=5)
            context.write('}\n', indent=4)
            context.write('\n', indent=4)
            context.write('resolve({\n', indent=4)
            context.write('room: room,\n', indent=5)
            context.write('foundUsers: foundUsers,\n', indent=5)
            context.write('participantCount: participants.length,\n', indent=5)
            context.write('scanTime: new Date().toISOString()\n', indent=5)
            context.write('});\n', indent=4)
            context.write('}).catch(function(error) {\n', indent=3)
            context.write('reject(error);\n', indent=4)
            context.write('});\n', indent=3)
            context.write('});\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('getRoomParticipants: function(roomId) {\n', indent=1)
            context.write('return new Promise(function(resolve, reject) {\n', indent=2)
            context.write('// Try multiple methods to get participant list\n', indent=3)
            context.write('var participants = [];\n', indent=3)
            context.write('\n', indent=3)
            context.write('// Method 1: Direct API call\n', indent=3)
            context.write('if (window.imvu && window.imvu.api) {\n', indent=3)
            context.write('window.imvu.api.getRoomParticipants(roomId).then(function(data) {\n', indent=4)
            context.write('resolve(data.participants || []);\n', indent=5)
            context.write('}).catch(function() {\n', indent=4)
            context.write('// Method 2: Check cached data\n', indent=5)
            context.write('if (window.roomParticipants && window.roomParticipants[roomId]) {\n', indent=5)
            context.write('resolve(window.roomParticipants[roomId]);\n', indent=6)
            context.write('} else {\n', indent=5)
            context.write('// Method 3: Join room briefly to get participant list\n', indent=6)
            context.write('resolve([]);\n', indent=6)
            context.write('}\n', indent=5)
            context.write('});\n', indent=4)
            context.write('} else {\n', indent=3)
            context.write('resolve([]);\n', indent=4)
            context.write('}\n', indent=3)
            context.write('});\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('isTargetUser: function(participant) {\n', indent=1)
            context.write('if (this.targetUsers.length === 0) return true; // Show all if no specific target\n', indent=2)
            context.write('\n', indent=2)
            context.write('return this.targetUsers.some(function(target) {\n', indent=2)
            context.write('return participant.username.toLowerCase().indexOf(target.toLowerCase()) !== -1 ||\n', indent=3)
            context.write('       participant.id.toString() === target ||\n', indent=3)
            context.write('       (participant.cid && participant.cid.toString() === target);\n', indent=3)
            context.write('});\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('detectInvisibleUsers: function(room) {\n', indent=1)
            context.write('var invisibleUsers = [];\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Check room history for users who were recently active\n', indent=2)
            context.write('if (window.userHistoryTracker && window.userHistoryTracker.roomHistory) {\n', indent=2)
            context.write('var roomHistory = window.userHistoryTracker.roomHistory;\n', indent=3)
            context.write('var recentThreshold = new Date(Date.now() - 30 * 60 * 1000); // 30 minutes ago\n', indent=3)
            context.write('\n', indent=3)
            context.write('Object.keys(roomHistory).forEach(function(userId) {\n', indent=3)
            context.write('var userRoomHistory = roomHistory[userId];\n', indent=4)
            context.write('var recentActivity = userRoomHistory.filter(function(activity) {\n', indent=4)
            context.write('return activity.roomId === room.id && new Date(activity.timestamp) > recentThreshold;\n', indent=5)
            context.write('});\n', indent=4)
            context.write('\n', indent=4)
            context.write('if (recentActivity.length > 0) {\n', indent=4)
            context.write('var user = window.imvu.userCache[userId];\n', indent=5)
            context.write('if (user && user.status === "offline") {\n', indent=5)
            context.write('invisibleUsers.push({\n', indent=6)
            context.write('userId: userId,\n', indent=7)
            context.write('username: user.username,\n', indent=7)
            context.write('status: "likely_invisible",\n', indent=7)
            context.write('isInvisible: true,\n', indent=7)
            context.write('lastSeen: user.lastSeen,\n', indent=7)
            context.write('detectionMethod: "room_history"\n', indent=7)
            context.write('});\n', indent=6)
            context.write('}\n', indent=5)
            context.write('}\n', indent=4)
            context.write('});\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('return invisibleUsers;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('updateTargetUsers: function(searchText) {\n', indent=1)
            context.write('if (!searchText.trim()) {\n', indent=2)
            context.write('this.targetUsers = [];\n', indent=3)
            context.write('return;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('this.targetUsers = searchText.split(",").map(function(user) {\n', indent=2)
            context.write('return user.trim();\n', indent=3)
            context.write('}).filter(function(user) {\n', indent=2)
            context.write('return user.length > 0;\n', indent=3)
            context.write('});\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('getAllRoomList: function() {\n', indent=1)
            context.write('// Get comprehensive room list from multiple sources\n', indent=2)
            context.write('var rooms = [];\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Add rooms from cache\n', indent=2)
            context.write('if (window.imvu && window.imvu.roomCache) {\n', indent=2)
            context.write('Object.values(window.imvu.roomCache).forEach(function(room) {\n', indent=3)
            context.write('rooms.push(room);\n', indent=4)
            context.write('});\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('// Add rooms from history\n', indent=2)
            context.write('if (window.userHistoryTracker && window.userHistoryTracker.roomHistory) {\n', indent=2)
            context.write('var roomHistory = window.userHistoryTracker.roomHistory;\n', indent=3)
            context.write('var roomIds = new Set();\n', indent=3)
            context.write('Object.values(roomHistory).forEach(function(userHistory) {\n', indent=3)
            context.write('userHistory.forEach(function(activity) {\n', indent=4)
            context.write('roomIds.add(activity.roomId);\n', indent=5)
            context.write('});\n', indent=4)
            context.write('});\n', indent=3)
            context.write('\n', indent=3)
            context.write('roomIds.forEach(function(roomId) {\n', indent=3)
            context.write('if (!rooms.find(function(r) { return r.id === roomId; })) {\n', indent=4)
            context.write('rooms.push({ id: roomId, name: "Room " + roomId });\n', indent=5)
            context.write('}\n', indent=4)
            context.write('});\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('return rooms;\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('updateProgress: function(percentage, message) {\n', indent=1)
            context.write('var progressFill = document.getElementById("scan-progress-fill");\n', indent=2)
            context.write('var progressText = document.getElementById("scan-progress-text");\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (progressFill) {\n', indent=2)
            context.write('progressFill.style.width = percentage + "%";\n', indent=3)
            context.write('}\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (progressText) {\n', indent=2)
            context.write('progressText.textContent = message;\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('updateResultsDisplay: function() {\n', indent=1)
            context.write('var resultsList = document.getElementById("scan-results-list");\n', indent=2)
            context.write('var resultsStats = document.getElementById("results-stats");\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (!resultsList) return;\n', indent=2)
            context.write('\n', indent=2)
            context.write('var totalUsers = 0;\n', indent=2)
            context.write('var totalRooms = this.scanResults.length;\n', indent=2)
            context.write('\n', indent=2)
            context.write('var html = "";\n', indent=2)
            context.write('this.scanResults.forEach(function(result) {\n', indent=2)
            context.write('totalUsers += result.foundUsers.length;\n', indent=3)
            context.write('html += "<div class=\\"result-item\\">";\n', indent=3)
            context.write('html += "<div class=\\"room-info\\">";\n', indent=3)
            context.write('html += "<h4>" + result.room.name + "</h4>";\n', indent=3)
            context.write('html += "<span class=\\"room-stats\\">(" + result.foundUsers.length + " users found)</span>";\n', indent=3)
            context.write('html += "</div>";\n', indent=3)
            context.write('html += "<div class=\\"found-users\\">";\n', indent=3)
            context.write('result.foundUsers.forEach(function(user) {\n', indent=3)
            context.write('html += "<div class=\\"user-item\\">";\n', indent=4)
            context.write('html += "<span class=\\"username\\">" + user.username + "</span>";\n', indent=4)
            context.write('html += "<span class=\\"status " + user.status + "\\">" + user.status + "</span>";\n', indent=4)
            context.write('if (user.isInvisible) {\n', indent=4)
            context.write('html += "<span class=\\"invisible-indicator\\">👁️</span>";\n', indent=5)
            context.write('}\n', indent=4)
            context.write('html += "</div>";\n', indent=4)
            context.write('});\n', indent=3)
            context.write('html += "</div>";\n', indent=3)
            context.write('html += "</div>";\n', indent=3)
            context.write('});\n', indent=2)
            context.write('\n', indent=2)
            context.write('resultsList.innerHTML = html;\n', indent=2)
            context.write('\n', indent=2)
            context.write('if (resultsStats) {\n', indent=2)
            context.write('resultsStats.textContent = totalUsers + " users found in " + totalRooms + " rooms";\n', indent=3)
            context.write('}\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('completeScan: function() {\n', indent=1)
            context.write('this.isScanning = false;\n', indent=2)
            context.write('this.updateProgress(100, "Scan completed");\n', indent=2)
            context.write('console.log("Room scan completed. Found", this.scanResults.length, "rooms with target users");\n', indent=2)
            context.write('},\n', indent=1)
            context.write('\n', indent=1)
            context.write('stopScan: function() {\n', indent=1)
            context.write('this.isScanning = false;\n', indent=2)
            context.write('this.updateProgress(0, "Scan stopped");\n', indent=2)
            context.write('}\n', indent=1)
            context.write('};\n', indent=0)
            context.write('\n')
            context.write('// Initialize the room scanner\n')
            context.write('advancedRoomScanner.init();\n')

        elif context.pattern == 'ROOM_SCANNER_CSS':
            context.write('.room-scanner-container {\n', indent=0)
            context.write('    background: #1a1a1a;\n', indent=1)
            context.write('    border: 1px solid #444;\n', indent=1)
            context.write('    border-radius: 8px;\n', indent=1)
            context.write('    padding: 20px;\n', indent=1)
            context.write('    color: #fff;\n', indent=1)
            context.write('    max-width: 800px;\n', indent=1)
            context.write('    margin: 0 auto;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.scanner-header {\n', indent=0)
            context.write('    margin-bottom: 20px;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.scanner-header h2 {\n', indent=0)
            context.write('    color: #4CAF50;\n', indent=1)
            context.write('    margin-bottom: 15px;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.scanner-controls {\n', indent=0)
            context.write('    display: flex;\n', indent=1)
            context.write('    gap: 10px;\n', indent=1)
            context.write('    align-items: center;\n', indent=1)
            context.write('    flex-wrap: wrap;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.scanner-controls input {\n', indent=0)
            context.write('    flex: 1;\n', indent=1)
            context.write('    min-width: 200px;\n', indent=1)
            context.write('    padding: 8px 12px;\n', indent=1)
            context.write('    background: #333;\n', indent=1)
            context.write('    border: 1px solid #555;\n', indent=1)
            context.write('    border-radius: 4px;\n', indent=1)
            context.write('    color: #fff;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.scanner-controls button {\n', indent=0)
            context.write('    padding: 8px 16px;\n', indent=1)
            context.write('    background: #4CAF50;\n', indent=1)
            context.write('    border: none;\n', indent=1)
            context.write('    border-radius: 4px;\n', indent=1)
            context.write('    color: white;\n', indent=1)
            context.write('    cursor: pointer;\n', indent=1)
            context.write('    transition: background 0.3s;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.scanner-controls button:hover {\n', indent=0)
            context.write('    background: #45a049;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.scanner-options {\n', indent=0)
            context.write('    display: flex;\n', indent=1)
            context.write('    gap: 20px;\n', indent=1)
            context.write('    margin: 20px 0;\n', indent=1)
            context.write('    padding: 15px;\n', indent=1)
            context.write('    background: #2a2a2a;\n', indent=1)
            context.write('    border-radius: 6px;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.option-group {\n', indent=0)
            context.write('    flex: 1;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.option-group label {\n', indent=0)
            context.write('    display: block;\n', indent=1)
            context.write('    margin-bottom: 8px;\n', indent=1)
            context.write('    color: #ccc;\n', indent=1)
            context.write('    font-size: 14px;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.progress-bar {\n', indent=0)
            context.write('    width: 100%;\n', indent=1)
            context.write('    height: 20px;\n', indent=1)
            context.write('    background: #333;\n', indent=1)
            context.write('    border-radius: 10px;\n', indent=1)
            context.write('    overflow: hidden;\n', indent=1)
            context.write('    margin-bottom: 10px;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.progress-fill {\n', indent=0)
            context.write('    height: 100%;\n', indent=1)
            context.write('    background: linear-gradient(90deg, #4CAF50, #45a049);\n', indent=1)
            context.write('    width: 0%;\n', indent=1)
            context.write('    transition: width 0.3s ease;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.scan-results {\n', indent=0)
            context.write('    margin-top: 20px;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.result-item {\n', indent=0)
            context.write('    background: #2a2a2a;\n', indent=1)
            context.write('    border: 1px solid #444;\n', indent=1)
            context.write('    border-radius: 6px;\n', indent=1)
            context.write('    padding: 15px;\n', indent=1)
            context.write('    margin-bottom: 10px;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.user-item {\n', indent=0)
            context.write('    display: flex;\n', indent=1)
            context.write('    align-items: center;\n', indent=1)
            context.write('    gap: 10px;\n', indent=1)
            context.write('    padding: 5px 0;\n', indent=1)
            context.write('    border-bottom: 1px solid #333;\n', indent=1)
            context.write('}\n\n', indent=0)
            context.write('.invisible-indicator {\n', indent=0)
            context.write('    font-size: 16px;\n', indent=1)
            context.write('    opacity: 0.7;\n', indent=1)
            context.write('}\n', indent=0)

        elif context.pattern == 'CHAT_ROOM_SEARCH_ENHANCED':
            context.write(context.line)
            context.write('\n// Enhanced room search with user detection\n')
            context.write('if (window.advancedRoomScanner) {\n', indent=0)
            context.write('// Add user search capability to room search\n', indent=1)
            context.write('var originalResults = searchResults;\n', indent=1)
            context.write('if (searchQuery.startsWith("user:")) {\n', indent=1)
            context.write('var username = searchQuery.substring(5).trim();\n', indent=2)
            context.write('window.advancedRoomScanner.targetUsers = [username];\n', indent=2)
            context.write('window.advancedRoomScanner.startScan("all");\n', indent=2)
            context.write('}\n', indent=1)
            context.write('}\n', indent=0)

        elif context.pattern == 'ROOM_LIST_ENHANCED':
            context.write(context.line)
            context.write('\n// Enhanced room list with participant preview\n')
            context.write('if (window.advancedRoomScanner) {\n', indent=0)
            context.write('// Add participant count and user detection to room list items\n', indent=1)
            context.write('rooms.forEach(function(room) {\n', indent=1)
            context.write('if (room.element) {\n', indent=2)
            context.write('var participantInfo = document.createElement("div");\n', indent=3)
            context.write('participantInfo.className = "participant-preview";\n', indent=3)
            context.write('participantInfo.innerHTML = "<span class=\\"participant-count\\">Loading...</span>";\n', indent=3)
            context.write('room.element.appendChild(participantInfo);\n', indent=3)
            context.write('\n', indent=3)
            context.write('// Async load participant info\n', indent=3)
            context.write('window.advancedRoomScanner.getRoomParticipants(room.id).then(function(participants) {\n', indent=3)
            context.write('var count = participants.length;\n', indent=4)
            context.write('var targetUsers = participants.filter(function(p) {\n', indent=4)
            context.write('return window.advancedRoomScanner.isTargetUser(p);\n', indent=5)
            context.write('});\n', indent=4)
            context.write('\n', indent=4)
            context.write('var html = count + " users";\n', indent=4)
            context.write('if (targetUsers.length > 0) {\n', indent=4)
            context.write('html += " (" + targetUsers.length + " targets)";\n', indent=5)
            context.write('participantInfo.classList.add("has-targets");\n', indent=5)
            context.write('}\n', indent=4)
            context.write('\n', indent=4)
            context.write('participantInfo.querySelector(".participant-count").textContent = html;\n', indent=4)
            context.write('});\n', indent=3)
            context.write('}\n', indent=2)
            context.write('});\n', indent=1)
            context.write('}\n', indent=0)